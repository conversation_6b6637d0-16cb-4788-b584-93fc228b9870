// Generated by github.com/davyxu/tabtoy
// DO NOT EDIT!!
// Version: 3.1.1
using System;
using System.Collections.Generic;

namespace Game.Hotfix.Proto
{ 	
	public enum MessageID
	{ 
		None = 0, // 预留值 
		Heart = 1, // 心跳 
		SecretSharePubKey = 11, // 公钥互换 
		SecretShareTest = 12, // 测试密钥 
		Login = 13, // 登录服务器 
		BattleRecordRead = 481, // 读取一场战报 
		BattleRecordReadGroup = 482, // 读取战报组 
		AccountList = 101, // 获取角色列表 
		LangTranslate = 501, // 翻译 
		UnionCreate = 601, // 创建联盟 
		UnionQuit = 602, // 退出联盟（主动） 
		UnionBrief = 603, // 获取联盟简略信息 
		UnionList = 604, // 获取联盟列表 
		UnionJoinApply = 605, // 联盟申请 
		UnionJoinApplyList = 606, // 联盟申请列表 
		UnionJoinApplyAgree = 607, // 联盟申请同意 
		UnionJoinApplyRefuse = 608, // 联盟申请拒绝 
		UnionSearch = 609, // 联盟搜索 
		UnionOneKeyJoin = 610, // 联盟一键入盟 
		UnionDisband = 611, // 联盟解散 
		UnionKickOut = 612, // 联盟踢出（踢人） 
		UnionEditNotify = 613, // 联盟编辑宣言 
		UnionChangeFlag = 614, // 联盟旗帜修改 
		UnionChangeName = 615, // 联盟改名 
		UnionChangeShortName = 616, // 联盟简称改名 
		UnionTransferLeader = 617, // 转让盟主 
		UnionInvite = 618, // 联盟邀请 
		UnionRallyPoint = 619, // 联盟集结点 
		UnionSendMail = 620, // 联盟邮件发送 
		UnionChangePermission = 621, // 联盟权限调整 
		UnionChangeJoinCondition = 622, // 联盟加入条件修改 
		UnionSetRecommendTech = 624, // 联盟科技推荐 
		UnionDeclareWar = 625, // 联盟宣战 
		UnionRemoveGarrison = 626, // 联盟移除驻军 
		UnionGiveUpCity = 627, // 联盟放弃城市 
		UnionOpenRecruit = 628, // 联盟开放招募 
		UnionRecordReq = 629, // 联盟日志 
		UnionGetCleanInactiveSwitch = 630, // 联盟获取清除不活跃成员开关状态 
		UnionApplyListChangePush = 631, // 联盟申请列表变更推送 
		UnionRoleInfoChangePush = 632, // 联盟成员信息变更推送 
		UnionFreeJoin = 633, // 联盟自由加入 
		UnionGiftList = 634, // 联盟礼物列表 
		UnionCleanInactive = 635, // 联盟清除不活跃成员 
		UnionCleanAllInactive = 636, // 联盟清除所有不活跃成员 
		UnionGiftReceive = 637, // 联盟礼物领取 
		UnionGiftReceiveAll = 638, // 联盟礼物一键领取 
		UnionMemberList = 639, // 联盟成员列表 
		UnionChangePosition = 640, // 联盟职位调整 
		UnionMemberOut = 641, // 联盟成员退出通知（关闭所有联盟界面） 
		UnionRankList = 642, // 联盟排行榜 
		UnionRandomShortName = 643, // 联盟随机简称 
		UnionCheckName = 644, // 联盟校验名称 
		UnionAnonymousGiftSwitch = 645, // 联盟匿名礼物开关 
		UnionHaveBeenJoin = 646, // 联盟玩家已经加入联盟，关闭创建界面 
		UnionTechList = 647, // 联盟科技列表 
		UnionTechStudy = 648, // 联盟科技研究 
		UnionTechAutoUpgradeSwitch = 649, // 联盟科技自动升级开关 
		UnionTechAutoUpgradeNotify = 650, // 联盟科技自动升级通知 
		UnionTechDonate = 651, // 联盟科技捐献 
		UnionChangeLang = 652, // 联盟语言修改 
		UnionMileStoneList = 653, // 联盟里程碑列表 
		PushMilestoneChange = 654, // 联盟里程碑变化推送 
		UnionMileStoneReward = 655, // 联盟里程碑奖励领取 
		PushUnionTechChange = 656, // 联盟科技变化推送 
		UnionHelpList = 657, // 联盟帮助列表 
		PushUnionHelp = 658, // 联盟请求成员帮助通知 
		UnionHelpAll = 659, // 联盟帮助全部 
		PushUnionHelpTips = 660, // 联盟帮助提示 
		PushUnionChange = 661, // 推送联盟变化信息 
		NovicePersonInfo = 801, // 新兵训练营个人数据 
		NoviceCompInfo = 802, // 新兵训练营挑战数据 
		NoviceRank = 803, // 新兵训练营排名数据 
		NoviceChallenge = 804, // 新兵训练营发起挑战 
		NoviceRefreshComp = 805, // 新兵训练营刷新对手 
		NoviceBuyChallengeTimes = 806, // 新兵训练营购买挑战次数 
		NoviceDrawAchieve = 807, // 新兵训练营领取排名成就 
		NoviceReports = 808, // 新兵训练营战报 
		PeakPersonInfo = 831, // 巅峰竞技场个人数据 
		PeakRank = 832, // 巅峰竞技场排名数据 
		PeakRankInterval = 833, // 巅峰竞技场指定排名区间数据 
		PeakChallenge = 834, // 巅峰竞技场发起挑战 
		PeakReports = 835, // 巅峰竞技场战报数据 
		ArenaOpenInfos = 891, // 竞技场开启信息 
		PushRoleArticles = 1003, // 推送物品 
		PushArticle = 1009, // 推送物品（恭喜获得） 
		RoleGM = 1004, // 开挂 
		RoleQueryLocal = 1005, // 查询本服角色信息 
		RoleQueryMulti = 1006, // 查询混服角色信息 
		PushPowerChange = 1007, // 推送战斗力变化 
		RoleQueryPowerBuild = 1010, // 查看角色战力组成 
		RoleClaimStamina = 1011, // 角色领取体力 
		RoleBuyStamina = 1012, // 角色购买体力 
		PushRoleStamina = 1013, // 推送角色体力信息 
		HeroUpgradeLevel = 1100, // 英雄升级请求 
		HeroUpgradeStar = 1101, // 英雄升星请求 
		HeroUpgradeSkill = 1102, // 英雄技能升级 
		HeroMapShowReq = 1103, // 英雄空投，回收显示请求 
		HeroUpgradeHonorLevel = 1104, // 英雄荣誉升级请求 
		HeroSynthetic = 1105, // 英雄合成 
		HeroQueryAttrBuild = 1151, // 查看英雄属性组成 
		EquipTakeOn = 1200, // 装备穿戴 
		EquipTakeOff = 1201, // 装备卸下 
		EquipLevelUp = 1202, // 装备升级 
		EquipPromotion = 1203, // 装备晋升 
		EquipReplace = 1204, // 装备替换 
		EquipProduce = 1205, // 装备制造 
		EquipReceive = 1206, // 装备领取 
		EquipResolve = 1207, // 装备拆解 
		EquipMaterialSynthesis = 1208, // 材料合成 
		EquipMaterialResolve = 1209, // 材料分解 
		ItemUse = 1300, // 道具使用 
		ItemBuy = 1301, // 道具购买 
		RecruitPoolsInfo = 1400, // 全量招募池数据 
		RecruitPoolChange = 1401, // 招募池推送 
		RecruitPoolDel = 1402, // 招募池删除 
		Recruit = 1403, // 招募 
		RecruitSetWish = 1404, // 设心愿英雄 
		RecruitDrawWish = 1405, // 领取心愿英雄 
		RecruitUpdatePreview = 1406, // 更新已读预告时间戳 
		BuildCreate = 1600, // 建筑建造 
		BuildUpgrade = 1601, // 建筑升级 
		BuildMove = 1602, // 建筑移动 
		BuildQueueFinish = 1603, // 队列完成 
		BuildQueueAccelerate = 1604, // 队列加速 
		BuildQueueHelp = 1606, // 队列帮助 
		PushBuildHelpAccelerate = 1607, // 推送建筑帮助加速 
		PushBuild = 1608, // 推送建筑 
		WorkerRenting = 1800, // 租赁工人 
		PushWorkerLock = 1801, // 推送工人 
		SoldierOp = 1700, // 操作士兵(训练,晋级) 
		SoldierTreatment = 1701, // 操作士兵(治疗) 
		PushSoldierChange = 1702, // 推送士兵 
		PushHospitalsChange = 1703, // 推送医院 
		SurvivorSummon = 1750, // 召唤幸存者 
		SurvivorStarUp = 1751, // 幸存者升星 
		SurvivorDispatch = 1752, // 幸存者派遣 
		SurvivorFastDispatch = 1753, // 幸存者快速派遣 
		TechStudy = 1900, // 科技研究 
		PushTechChange = 1901, // 推送科技变化 
		UavUpgrade = 2000, // 无人机升级 
		UavUseSkin = 2001, // 无人机换装 
		UavEquipComponent = 2002, // 无人机装备组件 
		UavOneClickEquipComponent = 2003, // 无人机一键装备 
		UavOneClickSynthesis = 2004, // 无人机一键合成 
		UavResearch = 2005, // 无人机研究 
		GiftTriggerList = 2950, // 触发礼包列表 
		PushGiftTriggerChange = 2951, // 触发礼包推送 
		PushGiftTriggerDel = 2952, // 触发礼包删除 
		StoreLoad = 3000, // 商店信息 
		StoreBuy = 3001, // 商店购买物品 
		ShopLoad = 3100, // 商城加载 
		ShopDailyDealLoad = 3101, // 商城每日特惠加载 
		PushShopDailyDeal = 3102, // 推送商城每日特惠信息 
		ShopDailyDealSwitch = 3103, // 商城每日特惠切换 
		ShopDailyDealClaimFree = 3104, // 商城每日特惠领取免费奖励 
		ShopDailyMustHaveLoad = 3106, // 商城每日必买加载 
		ShopDailyMustHaveClaim = 3107, // 商城每日必买领取积分奖励 
		ShopWeekDealLoad = 3111, // 商城每周特惠加载 
		ShopMonthCardLoad = 3116, // 商城超级月卡加载 
		ShopMonthCardClaim = 3117, // 商城超级月卡领取今日奖励 
		ShopWeekCardLoad = 3121, // 商城周卡加载 
		ShopWeekCardClaimFree = 3122, // 商城周卡领取免费奖励 
		ShopWeekCardClaimToday = 3123, // 商城周卡领取今日奖励 
		ShopGiftPackMallLoad = 3126, // 商城礼包商城加载 
		ShopDawnFundLoad = 3131, // 商城黎明基金加载 
		ShopDawnFundClaim = 3132, // 商城黎明基金领取奖励 
		ShopDiamondLoad = 3136, // 商城钻石直购加载 
		ShopGetWay = 3199, // 礼包链最新礼包 
		PushPayment = 3202, // 充值订单完成 
		PushPrivilege = 3204, // 特权变更 
		TeamModify = 3301, // 布阵修改 
		TeamQuery = 3302, // 阵容查询 
		BattleDebug = 3303, // 战斗调试 
		TeamDefendModify = 3304, // 防守队伍修改 
		PushTeamStatus = 3307, // 队伍状态变更 
		PushFormationTeam = 3308, // 推送布阵队伍变更 
		PushDefendTeam = 3309, // 推送防守队伍变更 
		TeamQueryPowerBuild = 3311, // 查看小队战力组成 
		DungeonLoad = 3401, // 关卡加载 
		DungeonFight = 3402, // 关卡战斗 
		DungeonSettle = 3403, // 关卡结算 
		DungeonClaimBox = 3404, // 关卡领取宝箱奖励 
		DungeonAccumulatedRewards = 3405, // 关卡挂机奖励 
		TaskList = 3450, // 任务列表 
		TaskChangePush = 3451, // 任务变更推送 
		TaskReceive = 3452, // 任务领取 
		TaskReceiveScoreRewards = 3453, // 任务领取积分奖励 
		InnerCityOccupyGrid = 3501, // 城内地图占领格子 
		InnerCityUnlockRegion = 3502, // 城内地图解锁区域 
		VipReceiveDailyPoints = 3550, // 每日免费VIP点数 
		VipReceiveDailyGift = 3551, // VIP等级每日免费礼包 
		PushVipChange = 3552, // 推送vip信息变化 
		RankList = 3601, // 排行榜列表 
		PushTradeChange = 3700, // 贸易推送货车变化 
		TradeShare = 3701, // 贸易货车分享 
		TradeLookUp = 3702, // 贸易查看货车 
		TradeCargoTransportList = 3703, // 贸易请求货车信息 
		TradeVanRecordList = 3705, // 贸易货车历史记录 
		TradeVanDetail = 3706, // 贸易货车详情 
		TradeVanRefresh = 3707, // 贸易刷新货车 
		TradeVanDepart = 3708, // 贸易货车出发 
		TradeVanSetOut = 3709, // 贸易货车出发（那个加号） 
		TradeVanReceiveReward = 3710, // 贸易到达后领取奖励 
		TradeVanRob = 3711, // 贸易掠夺货物 
		TradeVanWanted = 3712, // 贸易通缉玩家 
		TradeVanCollect = 3713, // 贸易收藏货车 
		TradeTrainAppointConductor = 3714, // 贸易指定列车长 
		TradeTrainRefreshGoods = 3716, // 贸易刷新火车货物 
		TradeTrainLineUp = 3717, // 贸易排队上车 
		TradeTrainDetail = 3718, // 贸易获取火车详细信息 
		TradeTrainThanks = 3719, // 贸易乘车感谢 
		TradeTrainThanksList = 3720, // 贸易感谢列表 
		TradeTrainInvite = 3721, // 贸易邀请vip乘客 
		TradeTrainFormation = 3722, // 贸易调整货车布阵 
		TradeTrainFormationOrder = 3723, // 贸易列调整货车布阵顺序 
		TradeTrainSelectVipPassengerReward = 3724, // 贸易选择vip乘客奖励 
		TradeTrainGuardAngelLike = 3725, // 贸易守护天使点赞 
		TradeTrainAgreeInvite = 3726, // 贸易同意邀请 
		TradeReceiveThanksReward = 3727, // 贸易领取感谢奖励 
		TradeTrainStartFightReq = 3728, // 贸易开始战斗 
		TradeTrainFightRecordList = 3729, // 贸易列车获取战斗记录 
		RelationshipBlackListOp = 3800, // 黑名单操作 
		ActivityOpenInfos = 4001, // 活动开启信息 
		PushActivityChange = 4002, // 活动开启信息变更推送 
		PushActivityDel = 4003, // 活动删除推送 
		ActivityConfig = 4004, // 活动配置 
		ActivityData = 4005, // 活动数据 
		ActivityDraw = 4006, // 活动通用领奖 
		ActivityBuy = 4007, // 活动通用购买 
		PushActivityHeroStarConfig = 4051, // 活动-英雄升星配置推送 
		PushActivityHeroStarData = 4052, // 活动-英雄升星数据推送 
		PushActivityRechargeConfig = 4061, // 活动-累充配置推送 
		PushActivityRechargeData = 4062, // 活动-累充数据推送 
		PushActivityBattlePassConfig = 4071, // 活动-战令配置推送 
		PushActivityBattlePassData = 4072, // 活动-战令数据推送 
		ActivityBattlePassTaskList = 4073, // 活动-战令全量任务 
		ActivityBattlePassDrawTask = 4074, // 活动-战令领取任务 
		PushActivityBattlePassTaskChange = 4075, // 活动-战令任务变动推送 
		PushActivityPowerConfig = 4081, // 活动-希望灯火配置推送 
		PushActivityPowerData = 4082, // 活动-希望灯火数据推送 
		RaceInfo = 4601, // 军备竞赛个人信息 
		RaceTaskDraw = 4602, // 军备竞赛领取任务宝箱 
		RaceBoxDraw = 4603, // 军备竞赛领取大宝箱 
		PushRaceScoreChange = 4604, // 军备竞赛积分变动推送 
		RaceRank = 4605, // 军备竞赛排行榜 
		TowerLoad = 5001, // 荣耀远征加载 
		TowerChoose = 5002, // 荣耀远征选择章节 
		TowerFight = 5003, // 荣耀远征战斗 
		PaymentOrder = 40001, // 充值下单 
		CameraInit = 42200, // 镜头初始化 
		CameraRemove = 42201, // 镜头移除 
		CameraMove = 42202, // 镜头移动 
		MarchCreate = 42203, // 创建行军 
		MapHallMove = 45000, // 本服迁城 
		PushMapInfo = 45001, // 地图推送 
		PushHallRelocation = 45002, // 玩家大地图新位置推送 
		PushMarch = 45003, // 行军推送 
		MailLoad = 47001, // 邮件加载 
		MailSend = 47002, // 邮件发送 
		MailDetail = 47003, // 邮件详情 
		MailRead = 47004, // 邮件阅读 
		MailClaim = 47005, // 邮件领取附件 
		MailDelete = 47006, // 邮件删除 
		MailFavorite = 47007, // 邮件收藏 
		MailUnfavorite = 47008, // 邮件取消收藏 
		MailLike = 47009, // 邮件点赞 
		PushMailAdd = 47051, // 推送新增邮件 
		PushMailDel = 47052, // 推送删除邮件 
		ChatList = 47101, // 聊天列表 
		ChatSend = 47102, // 聊天发送 
		ChatOp = 47103, // 聊天消息操作 
		ChatReport = 47104, // 聊天举报 
		PushChatMsg = 47105, // 聊天推送 
		ChatPrivateList = 47106, // 聊天私聊列表 
		ChatDeletePrivate = 47107, // 聊天私聊删除 
		ChatNodeList = 47108, // 聊天节点列表 
		PushChatNode = 47109, // 聊天节点变化推送 
		ChatRelay = 47110, // 聊天信息转发 
		ChatGoldenEggReceive = 47200, // 聊天领取金蛋 
		ChatGoldenEggRecord = 47201, // 聊天查看金蛋记录 
		ChatUnionAnnounceMark = 47210, // 联盟公告标记 
		ChatUnionMarkAnnounceSort = 47211, // 联盟标记公告排序 
		ChatUnionAnnounceUrgent = 47212, // 联盟文本公告紧急发布 
		ChatUnionAnnounceDelete = 47213, // 联盟公告删除 
		ChatUnionAnnounceVote = 47214, // 联盟公告投票 
		FileServerUpload = 48200, // 文件上传 
		PushFileServerUploadResult = 48201, // 推送文件上传结果 
	}
	
	public enum ResultCode
	{ 
		Success = 0, // 成功 
		SuccessWithoutSave = 1, // 成功且不保存数据 
		Failed = 1001, // 失败 
		Timeout = 1002, // 超时 
		TokenNotFound = 1003, // token未找到 
		TokenVerifyFailed = 1004, // token验证失败 
		FeatureNotOpen = 1005, // 功能未开放 
		WebRouterNotFound = 2001, // 路由未找到 
		WebInternalErr = 2002, // 内部错误 
		WebParamNotFound = 2003, // 参数未找到 
		WebParamInvalidFormat = 2004, // 参数格式不正确 
		WebParamReadFailed = 2005, // 参数读取失败 
		WebParamInvalidLength = 2006, // 参数长度不正确 
		WebParamVerifyFailed = 2007, // 参数验证失败 
		WebParamMissRequired = 2008, // 缺少必要参数 
		WebParamMissSign = 2009, // 缺少签名字段 
		WebRateLimitGetFailed = 2021, // 获取限流数据失败 
		WebRateLimitNotAllow = 2022, // 系统繁忙，请稍后再试 
		WebInvalidTimestamp = 2023, // 当前时间与服务器时间相差太多 
		WebSignVerifyFailed = 2024, // 签名校验失败 
		WebSignInFailed = 2101, // 登录验证失败 
		WebCreateTokenFailed = 2102, // 生成验证token失败 
		WebAccountStatusForbid = 2103, // 已封号 
		ProtoUnarshalFailed = 10001, // 协议解码失败 
		ProtoMarshalFailed = 10002, // 协议编码失败 
		ProtocolNotFound = 10003, // 协议未找到 
		RemoteCallFailed = 10004, // 远程调用失败 
		ConfigNotFound = 10005, // 配置未找到 
		IDZero = 10006, // id为0 
		DevelopOnly = 10007, // 仅在开发环境下可用 
		PushFailed = 10008, // 推送失败 
		ParamError = 10011, // 参数错误 
		ParamError2 = 10012, // 参数错误2 
		ParamError3 = 10013, // 参数错误3 
		ParamError4 = 10014, // 参数错误4 
		ParamError5 = 10015, // 参数错误5 
		ParamError6 = 10016, // 参数错误6 
		ParamError7 = 10017, // 参数错误7 
		ParamError8 = 10018, // 参数错误8 
		ParamError9 = 10019, // 参数错误9 
		ParamError10 = 10020, // 参数错误10 
		ConfigRewardError = 10021, // 配置奖励错误 
		ConfigConsumeError = 10022, // 配置消耗错误 
		ChangeShowFailed = 10023, // 物品变更失败 
		ChangeShowFailed2 = 10024, // 物品变更失败2 
		UnlockHeroNotFound = 10031, // 英雄不存在 
		UnlockHeroLevelLess = 10032, // 英雄等级不足 
		UnlockHeroStarLess = 10033, // 英雄星级不足 
		LevelNotEnough = 10034, // 等级不足 
		StarNotEnough = 10035, // 星级不足 
		DBCreateFailed = 10041, // 数据库创建失败 
		DBQueryFailed = 10042, // 数据库查询失败 
		DBUpdateFailed = 10043, // 数据库更新失败 
		EncodeFailed = 10044, // 编码失败 
		DecodeFailed = 10045, // 解码失败 
		EncryptFailed = 10046, // 加密失败 
		DecryptFailed = 10047, // 解密失败 
		RewardClaimed = 10048, // 奖励已领取 
		RewardNotActive = 10049, // 奖励未激活 
		RewardNotMatchCondition = 10050, // 不满足获取条件,无法领取奖励 
		RewardsClaimEmpty = 10051, // 没有奖励可以领取 
		ItemNotEnough = 10100, // 道具不足 
		Resource1NotEnough = 10101, // 体力不足 
		Resource2NotEnough = 10102, // 粮食不足 
		Resource3NotEnough = 10103, // 铁矿不足 
		Resource4NotEnough = 10104, // 金币不足 
		Resource5NotEnough = 10105, // 英雄经验不足 
		Resource6NotEnough = 10106, // 钻石不足 
		Resource7NotEnough = 10107, // 荣誉积分不足 
		Resource8NotEnough = 10108, // 远征奖章不足 
		Resource9NotEnough = 10109, // 光荣勋章不足 
		Resource10NotEnough = 10110, // 技能勋章不足 
		Resource11NotEnough = 10111, // 闪亮金币不足 
		Resource12NotEnough = 10112, // VIP点数不足 
		Resource13NotEnough = 10113, // 金砖不足 
		Resource14NotEnough = 10114, // 原油不足 
		Resource15NotEnough = 10115, // 同盟贡献点不足 
		SessionNotReady = 11001, // 连接尚未准备，无法发送其它信息 
		ShareKeyGenFailed = 11002, // 密钥生成失败 
		ShareKeyTestFailed = 11003, // 秘钥测试失败 
		LoginTokenVerifyFailed = 11004, // 登录秘钥验证失败 
		ServiceVersionsNotFound = 11005, // 服务版本号未找到 
		ServerIDEmpty = 11006, // 未设置区服Id 
		AccountListIsEmpty = 11007, // 账号角色列表为空 
		RolePollerNotFound = 12001, // 角色轮询器未找到 
		RoleGetTimeout = 12002, // 获取角色数据超时 
		RoleLoginFailed = 12003, // 登录失败 
		RoleNeedCreate = 12004, // 需要创建角色 
		RoleCreateFailed = 12005, // 角色创建失败 
		RoleCreatedBefore = 12006, // 角色已创建 
		RoleGMBagExecuteFailed = 12007, // 角色GM背包执行失败 
		RoleGMSGExecuteFailed = 12008, // 角色GM SG 执行失败 
		HeroNotExist = 12301, // 英雄不存在 
		HeroAlreadyExist = 12302, // 英雄已存在 
		HeroStarFull = 12303, // 英雄已满星 
		HeroLevelFull = 12304, // 英雄已满级 
		HeroSkillLevelFull = 12305, // 英雄技能已满级 
		HeroTypeNonConformance = 12306, // 英雄类型不符合 
		HeroSkillUnlock = 12307, // 技能未解锁 
		HeroCanNotInTeam = 12308, // 英雄不能在队伍内 
		HeroHonorLevelFull = 12309, // 英雄晋升已满级 
		HeroCreateFailed = 12340, // 英雄创建失败 
		BuildAlreadyExists = 12401, // 建筑已经存在 
		BuildDemandNotSatisfied = 12402, // 建造或者升级条件不满足 
		BuildCreateCostNotEnough = 12403, // 建造所需资源不足 
		BuildCanNotupgrade = 12404, // 建筑不能升级 
		BuildNotExists = 12405, // 建筑不存在 
		BuildIsCreatingOrUpgrading = 12406, // 建筑正在创建或者升级 
		BuildUpgradeCostNotEnough = 12407, // 升级所需资源不足 
		BuildCanNotMove = 12408, // 建筑不能移动 
		BuildQueueNotExists = 12421, // 队列不存在 
		BuildQueueNotFinish = 12422, // 队列未完成 
		BuildQueueHasBeenHelped = 12423, // 队列已经帮助 
		BuildQueueIsFinish = 12424, // 队列已完成 
		BuildQueueCanNotHelp = 12425, // 队列不能帮助 
		BuildQueueCanNotAccelerate = 12426, // 队列不能加速 
		BuildQueueItemCanNotAccelerate = 12427, // 物品不能加速此队列 
		BuildQueueItemAccelerateNoteEnough = 12428, // 道具加速不足 
		BuildQueueDiamondAccelerateNoteEnough = 12429, // 钻石加速不足 
		BuildWorkerNotExists = 12430, // 工人不存在 
		BuildWorkeruIsNotIdle = 12431, // 工人不是空闲状态 
		EquipAlreadyWorn = 17501, // 已穿戴 
		EquipNotFound = 12701, // 装备未找到 
		EquipLevelFull = 12702, // 装备已满级 
		EquipPromotionLevelFull = 12703, // 装备已满晋升级 
		EquipNotProducible = 12704, // 装备不可生产 
		EquipTargetSame = 12705, // 目标相同 
		EquipNotTargetObject = 12706, // 没有目标英雄对象 
		EquipCreateFailed = 12707, // 装备创建失败 
		EquipCantUpgradeLevel = 12708, // 该装备不能升级 
		EquipCantUpgradePromotion = 12709, // 该装备不能晋升 
		EquipTakeOnNoMatch = 12710, // 没有可穿戴装备 
		EquipTakeOnCurrentAlready = 12711, // 已穿戴当前装备 
		EquipTakeOffEmpty = 12712, // 没有装备可卸下 
		EquipResolveIDEmpty = 12713, // 请选择分解对象 
		EquipResolveDressed = 12714, // 已穿戴的装备不可分解 
		EquipMaterialCantSynthesis = 12715, // 该材料不可合成 
		EquipMaterialCantResolve = 12716, // 该材料不可分解 
		ItemNotFound = 12750, // 道具未找到 
		ItemUseFailed = 12751, // 道具使用失败 
		ItemAddFailed = 12752, // 道具添加失败 
		RecruitFailed = 12801, // 招募失败 
		RecruitWishSetFirst = 12802, // 请先设置心愿英雄 
		RecruitWishTimesNotEnough = 12803, // 心愿次数不足 
		DungeonCantFightMax = 13100, // 已达最大关卡,无法继续挑战 
		DungeonLastFightNotMatch = 13101, // 结算关卡与最后挑战的关卡不匹配 
		DungeonFightFirstMission = 13102, // 尚未开启关卡挑战 
		DungeonBoxIDGreaterThenMax = 13103, // 该关卡尚未通关 
		DungeonBoxClaimed = 13104, // 该关卡奖励已领取 
		DungeonFightRepeated = 13105, // 无法重复挑战 
		DungeonFightNotNextID = 13106, // 无法跳关挑战 
		VipExpire = 13400, // vip已过期 
		TradeVanSetOutFull = 13500, // 城际贸易车已满 
		TradeTimesNotEnough = 13501, // 城际贸易次数不足 
		TradeNotFound = 13502, // 找不到货车数据 
		TradeHaveBeenDepart = 13503, // 货车已出发 
		TradeNotArrived = 13504, // 货车未到达 
		TradeRecordNotFound = 13505, // 找不到货车记录 
		TradeWanted = 13506, // 已通缉 
		TradeRobTimesLimit = 13507, // 掠夺次数限制 
		TradeCreateCargoTransportFailed = 13508, // 创建货物运输失败 
		TradeTrainNotFound = 13509, // 找不到火车数据 
		TradeCannotSummonTrain = 13510, // 无法召唤火车 
		TradeNotInPrepareTime = 13511, // 当前不在准备时间 
		TradeTrainThanksAlready = 13512, // 已经感谢 
		TradeNoFormationPermission = 13513, // 布阵权限不足 
		TradeNoPermission = 13514, // 没有权限 
		TradeRepeatLike = 13515, // 重复点赞 
		TradeRepeatInvite = 13516, // 重复邀请 
		TradeInviteCD = 13517, // 邀请cd 
		TradeInviteExpired = 13518, // 邀请已过期 
		TradeTrainInProtectTime = 13519, // 列车处于保护时间 
		TradeVanArrived = 13520, // 货车已到达 
		TradeVanInProtectTime = 13521, // 货车处于保护时间 
		TradeTrainRobTimesLimit = 13522, // 货车列车掠夺次数限制 
		TechPreTechNotActive = 13600, // 前置科技未激活 
		TechPreTechNotStudy = 13601, // 前一个科技未学习 
		TechMaxLevel = 13602, // 科技等级达到最大 
		AccountPollerNotFound = 40001, // 账号轮询器未找到 
		AccountGetTimeout = 40002, // 获取账号数据超时 
		AccountCreateFailed = 40003, // 账号创建失败 
		UnionInvalidWord = 41001, // 不可用名称 
		UnionCreateFailed = 41002, // 创建失败 
		UnionNameLength = 41003, // 名称长度错误 
		UnionNameRepeat = 41004, // 名称重复 
		UnionNotFound = 41005, // 找不到联盟 
		UnionApplyAgree = 41006, // 同意申请 
		UnionApplyRefuse = 41007, // 拒绝申请 
		UnionApplyRepeat = 41008, // 重复申请 
		UnionNotEnoughApplyCondition = 41009, // 不满足申请条件 
		UnionNotEnoughCreateCondition = 41010, // 不满足创建条件 
		UnionMemberNumLimit = 41011, // 人数已满 
		UnionNoFoundMeetCondition = 41012, // 未找到符合条件联盟 
		UnionNoEnterUnion = 41013, // 未加入联盟 
		UnionNoPermission = 41014, // 无权限 
		UnionNoticeLengthErr = 41015, // 公告长度错误 
		UnionConditionParamsErr = 41016, // 条件参数错误 
		UnionGiftHasReceived = 41017, // 礼物已领取 
		UnionApplyInfoTimeout = 41018, // 申请信息超时 
		UnionNotMember = 41019, // 不是联盟成员 
		UnionMemberNumGT1 = 41020, // 联盟人数大于1 
		UnionMemberCannotOut = 41021, // 联盟成员不能退出 
		Union24HourCannotJoin = 41022, // 24小时不能加入联盟 
		UnionNotOpenUnionBuild = 41023, // 没有开启联盟建筑 
		UnionHasJoinUnion = 41024, // 已经加入联盟 
		UnionPermissionNumLimit = 41025, // 权限数量限制 
		UnionPowerNotEnough = 41026, // 联盟战力不足 
		UnionBaseLevelNotEnough = 41027, // 联盟总部等级不足 
		UnionGiftLevelNotEnough = 41028, // 礼物等级不足 
		UnionGiftNotExist = 41029, // 礼物不存在 
		UnionMemberHeadquartersLevelNotEnough = 41030, // 联盟成员总部等级不足 
		UnionTechUpgrading = 41031, // 联盟科技升级中 
		UnionTechLevelMax = 41032, // 联盟科技已满级 
		UnionTechDonateTimesMax = 41033, // 联盟科技捐献次数已达上限 
		UnionTechNotEnough = 41034, // 联盟科技不存在 
		UnionOnlyOneRecommendTech = 41035, // 联盟科技只能推荐一个 
		UnionMileStoneEnd = 41036, // 联盟里程碑已结束 
		UnionMileStoneRewardNoReceive1 = 41037, // 联盟里程碑礼物未领取，不能转让 
		UnionMileStoneRewardNoReceive2 = 41038, // 联盟里程碑礼物未领取，不能被转让 
		UnionPreTechLocked = 41039, // 联盟科技前置科技未锁定 
		UnionTechTagLocked = 41040, // 联盟科技未解锁 
		UnionTechWaitUpgrade = 41041, // 联盟科技等待升级 
		UnionMilestoneNotMember = 41042, // 联盟里程碑完成时不是成员 
		UnionGiftTimeout = 41043, // 礼物已过期 
		UnionTradeHaveTrain = 41044, // 当前已经有列车 
		UnionTradeTrainSummonFail = 41045, // 召唤列车失败 
		UnionTradeJoinTimeNotEnough = 41046, // 加入联盟时间不足，不能参与列车 
		ArenaSrvTimeout = 42000, // 获取竞技场服务超时 
		NoviceInfoNotCreate = 42011, // 玩家数据未初始化 
		NoviceChallengeRankNotValid = 42012, // 挑战的排名不合法 
		NoviceChallengeNotMatch = 42013, // 挑战的数据不匹配 
		PaymentNotSupportTokenPay = 43001, // 不支持代币支付 
		PaymentPriceNotMatch = 43002, // 价格不匹配 
		PaymentOrderNotFound = 43003, // 订单未找到 
		PaymentOrderInvalid = 43004, // 订单无效 
		PaymentChannelNotFound = 43005, // 充值渠道未找到 
		PaymentOrderUpdateFailed = 43006, // 订单更新失败 
		PaymentOrderDeliveryFailed = 43007, // 订单发货失败 
		PaymentOrderCheckFailedRemoteCall = 43008, // 下单前检查失败(远程调用失败) 
		FormationHeroRepeatInTeam = 44001, // 同一个队伍中英雄不可重复 
		FormationPositionRepeatInTeam = 44002, // 同一个队伍中位置不可重复 
		FormationHeroRepeatInAllTeams = 44003, // 所有队伍中英雄不可重复 
		FormationPositionRepeatInAllTeams = 44004, // 所有队伍中位置不可重复 
		FormationInvalidPos = 44005, // 无效位置 
		ChatOnlyRead = 45001, // 聊天仅读 
		ChatStop = 45002, // 聊天功能停用 
		ChatMsgNotFound = 45003, // 聊天消息不存在 
		ChatCannotReply = 45004, // 聊天不支持回复 
		ChatCannotLike2Self = 45005, // 聊天不能点赞自己 
		ChatCannotLike = 45006, // 聊天不支持点赞 
		ChatCannotCheers = 45007, // 聊天不支持喝彩 
		ChatRevokeTimeout = 45008, // 聊天消息撤回超时 
		ChatCannotRevoke = 45009, // 聊天不支持撤回 
		ChatCannotReport = 45010, // 聊天不支持举报 
		ChatStopVote = 45011, // 聊天投票已停止 
		ChatVoted = 45012, // 聊天已投票 
		ChatAnnouncementSingleChoice = 45013, // 聊天当前公告仅支持单选 
		FileServerExtensionNotAllowed = 46001, // 文件扩展名不允许 
		FileServerSizeExceeded = 46002, // 文件大小超出限制 
		FileServerUploadFailed = 46003, // 文件上传失败 
		FileServerCheckFailed = 46004, // 文件审核失败 
	}
		
	public partial class messageDefine : tabtoy.ITableSerializable
	{ 
		public MessageID MessageID = MessageID.None;
		public string PkgName = string.Empty;
		public string Request = string.Empty;
		public string Response = string.Empty;
		public bool IgnoreErrorTips = false;
		
		#region Deserialize Code
		public void Deserialize( tabtoy.TableReader reader )
		{
			UInt32 mamaSaidTagNameShouldBeLong = 0;
            while ( reader.ReadTag(ref mamaSaidTagNameShouldBeLong) )
            {
 				switch (mamaSaidTagNameShouldBeLong)
				{ 
					case 0xa0000:
                	{
						reader.ReadEnum( ref MessageID );
                	}
					break;
					case 0x80001:
                	{
						reader.ReadString( ref PkgName );
                	}
					break;
					case 0x80002:
                	{
						reader.ReadString( ref Request );
                	}
					break;
					case 0x80003:
                	{
						reader.ReadString( ref Response );
                	}
					break;
					case 0x90004:
                	{
						reader.ReadBool( ref IgnoreErrorTips );
                	}
					break;
					
                    default:
                    {
                        reader.SkipFiled(mamaSaidTagNameShouldBeLong);                            
                    }
                    break;
				}
			}
		}
		#endregion 
	}
	
	public partial class resultcode : tabtoy.ITableSerializable
	{ 
		public ResultCode ResultCode = ResultCode.Success;
		public bool Show = false;
		
		#region Deserialize Code
		public void Deserialize( tabtoy.TableReader reader )
		{
			UInt32 mamaSaidTagNameShouldBeLong = 0;
            while ( reader.ReadTag(ref mamaSaidTagNameShouldBeLong) )
            {
 				switch (mamaSaidTagNameShouldBeLong)
				{ 
					case 0xa0000:
                	{
						reader.ReadEnum( ref ResultCode );
                	}
					break;
					case 0x90001:
                	{
						reader.ReadBool( ref Show );
                	}
					break;
					
                    default:
                    {
                        reader.SkipFiled(mamaSaidTagNameShouldBeLong);                            
                    }
                    break;
				}
			}
		}
		#endregion 
	}
	

	// Combine struct
	public partial class Table
	{ 
		// table: messageDefine
		public List<messageDefine> messageDefine = new List<messageDefine>(); 

		// Indices
		public Dictionary<MessageID,messageDefine> messageDefineByMessageID = new Dictionary<MessageID,messageDefine>();
		
		
		

		public void ResetData( )
		{   
			messageDefine.Clear();  
			messageDefineByMessageID.Clear();
				
		}
		
		public void Deserialize( tabtoy.TableReader reader )
		{	
			reader.ReadHeader();

			UInt32 mamaSaidTagNameShouldBeLong = 0;
            while ( reader.ReadTag(ref mamaSaidTagNameShouldBeLong) )
            {
				if (mamaSaidTagNameShouldBeLong == 0x6f0000)
				{
                    var tabName = string.Empty;
                    reader.ReadString(ref tabName);
					switch (tabName)
					{ 
						case "messageDefine":
						{
							reader.ReadStruct(ref messageDefine);	
						}
						break;
						default:
						{
							reader.SkipFiled(mamaSaidTagNameShouldBeLong);                            
						}
						break;
					}
				}
			}
		}

		public void IndexData( string tabName = "")
		{ 	
			if (tabName == "" || tabName == "messageDefine")
			{
				foreach( var kv in messageDefine )
				{
					messageDefineByMessageID[kv.MessageID] = kv;
				}
				
			}
			
		}
		
	}
}
