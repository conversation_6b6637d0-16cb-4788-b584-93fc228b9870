using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class ScrollNodePage
    {
        private bool hasInit = false; //是否已经初始化过
        private ScrollRect _scrollRect;
        private GameObject itemObj;

        private int childCount = 0;
        public int ChildCount => childCount;//总节点数
        private List<Image> imgList;
        private float triggerRatio = 0.3f;
        private float triggerWidth;
        private float beginX;
        private float endX;
        private int curScrollIndex = 1;
        public int CurScrollIndex => curScrollIndex;//获取当前展示界面索引

        private float itemWidth;
        private Transform contentRoot;
        private Action<int, GameObject> callback;
        private Action<int> switchFunc;

        struct PosNode
        {
            //左闭右开
            public int index;
            public float min; //左闭极小值
            public float max; //右开极大值

            public void Init(int a, float b, float c)
            {
                index = a;
                min = b;
                max = c;
            }
        }

        private List<PosNode> PosNodeList;
        private List<PosNode> RevPosNodeList;

        #region 外部调用接口
        public void OnInit(ScrollRect _scroll, GameObject obj,Action<int,GameObject> _callback = null,Action<int> _switchLogic = null)
        {
            if (hasInit) return;
            PosNodeList ??= new List<PosNode>();
            RevPosNodeList ??= new List<PosNode>();

            _scrollRect = _scroll;
            itemObj = obj;
            itemWidth = obj.GetComponent<RectTransform>().rect.width;

            triggerWidth = itemWidth * triggerRatio;
            _scrollRect = _scrollRect.GetComponent<ScrollRect>();
            contentRoot = _scrollRect.content.transform;

            var event1 = new EventTrigger.Entry { eventID = EventTriggerType.BeginDrag, };
            event1.callback.AddListener((eventData) => { OnBeginDrag(); });
            var event2 = new EventTrigger.Entry { eventID = EventTriggerType.EndDrag, };
            event2.callback.AddListener((eventData) => { OnEndDrag(); });

            var eventTrigger = _scrollRect.gameObject.GetComponent<EventTrigger>();
            if (eventTrigger == null)
            {
                eventTrigger = _scrollRect.gameObject.AddComponent<EventTrigger>();
            }

            eventTrigger.triggers.Add(event1);
            eventTrigger.triggers.Add(event2);

            callback = _callback;
            switchFunc = _switchLogic;
            hasInit = true;
        }
        
        public void UpdateChildCount(int sumCount)
        {
            _scrollRect.normalizedPosition = new Vector2(0, 1);
            curScrollIndex = 1;
            _scrollRect.enabled = sumCount > 1;
            childCount = sumCount;
            CalSectionList();
            CalRevSectionList();
            ToolScriptExtend.RecycleOrCreate(itemObj, contentRoot, sumCount);
            for (var i = 0; i < sumCount; i++)
            {
                var child = contentRoot.GetChild(i);
                callback?.Invoke(i, child.gameObject);
            }

            var layout = contentRoot.GetComponent<HorizontalLayoutGroup>();
            UpdateContentWidth(sumCount, itemWidth, layout.spacing, layout.padding.left, layout.padding.right);
        }

        //查看左边的界面
        public void CheckLeftPage()
        {
            CheckPageLogic(true);
        }
        
        //查看右边的界面
        public void CheckRightPage()
        {
            CheckPageLogic(false);
        }

        //查看指定索引界面
        public void MoveToPageByIndex(int index,bool needScrollAnim)
        {
            MoveToTarget(index,needScrollAnim);
        }

        //更新指定界面逻辑,从1开始
        public void UpdatePageByIndex(int index)
        {
            if (index <= 0 || index > childCount) return;
            if (index > contentRoot.childCount) return;
            var child = contentRoot.GetChild(index-1);
            callback?.Invoke(index-1, child.gameObject);
        }
        
        #endregion
       
        
        #region 核心内部逻辑
        private void UpdateContentWidth(int sumCount, float itemWidth, float space, float leftSpace = 0,
            float rightSpace = 0)
        {
            var sumWidth = leftSpace + itemWidth * sumCount + space * (sumCount - 1) + rightSpace;
            _scrollRect.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, sumWidth);
        }

        private void OnBeginDrag()
        {
            if (childCount <= 1) return;
            beginX = _scrollRect.content.anchoredPosition.x;
        }

        private void OnEndDrag()
        {
            if (childCount <= 1) return;
            endX = _scrollRect.content.anchoredPosition.x;
            var dirValue = beginX - endX;

            _scrollRect.StopMovement();
            var x = _scrollRect.content.anchoredPosition.x;
            x = Math.Abs(x);
            int index = 0;
            
            if (dirValue >= 0)
            {
                //向右滑动
                index = GetChildIndex(x);
                if (curScrollIndex > 1 && index == 1)
                {
                    return;
                }
            }
            else
            {
                //向左滑动
                index = GetRevChildIndex(x);
                if (curScrollIndex == 1 && index == 1)
                {
                    return;
                }
            }
            MoveToTarget(index, true);
        }

        private float GetTargetX(int index)
        {
            return (index - 1) * itemWidth;
        }

        private void CalSectionList()
        {
            PosNodeList.Clear();
            //存储从左向右滑动数据
            float lastValue = 0;
            for (int i = 1; i <= childCount; i++)
            {
                var node = new PosNode();
                if (i == 1)
                {
                    node.Init(i, 0, triggerWidth);
                    lastValue = triggerWidth;
                }
                else if (i == childCount)
                {
                    var len = itemWidth * (i - 1);
                    node.Init(i, lastValue, len);
                }
                else
                {
                    float left = lastValue;
                    float right = lastValue + itemWidth;
                    node.Init(i, left, right);
                    lastValue = right;
                }

                PosNodeList.Add(node);
            }
        }

        private void CalRevSectionList()
        {
            RevPosNodeList.Clear();
            //存储从右向左滑动数据
            float lastValue = 0;
            for (int i = childCount; i >= 1; i--)
            {
                var node = new PosNode();
                if (i == 1)
                {
                    node.Init(i, 0, lastValue);
                }
                else if (i == childCount)
                {
                    var len = itemWidth * (i - 1);
                    var value = len - triggerWidth;
                    node.Init(i, value, len);
                    lastValue = value;
                }
                else
                {
                    float left = lastValue - itemWidth;
                    float right = lastValue;
                    node.Init(i, left, right);
                    lastValue = left;
                }

                RevPosNodeList.Add(node);
            }
        }

        private int GetChildIndex(float x)
        {
            if (x == 0)
            {
                return 1;
            }

            if (x == (itemWidth * (childCount - 1)))
            {
                return childCount;
            }

            int index = 1;
            foreach (var node in PosNodeList)
            {
                if (x >= node.min && x < node.max)
                {
                    index = node.index;
                    return index;
                }
            }

            return index;
        }

        private int GetRevChildIndex(float x)
        {
            if (x == 0)
            {
                return 1;
            }

            if (x == (itemWidth * (childCount - 1)))
            {
                return childCount;
            }

            int index = 1;
            foreach (var node in RevPosNodeList)
            {
                if (x > node.min && x <= node.max)
                {
                    index = node.index;
                    return index;
                }
            }
            return index;
        }

        //查看界面逻辑
        private void CheckPageLogic(bool isLeft)
        {
            if (childCount <= 1) return;
            _scrollRect.StopMovement();
            var x = _scrollRect.content.anchoredPosition.x;
            x = Math.Abs(x);
            int index = 0;
            
            index = GetRevChildIndex(x);
            if (isLeft)
            {
                //向左滑动
                index--;
                if (index == -1)
                {
                    return;
                }
                if (curScrollIndex == 1 && index == 1)
                {
                    return;
                }
            }
            else
            {
                index++;
                if (index > childCount)
                {
                    return;
                }
                if (curScrollIndex > 1 && index == 1)
                {
                    return;
                }
            }
            
            MoveToTarget(index, true);
        }

        /// <summary>
        /// 滚动到指定索引界面
        /// </summary>
        /// <param name="index">指定索引</param>
        /// <param name="needScrollAnim">是否显示滚动动画</param>
        private void MoveToTarget(int index,bool needScrollAnim)
        {
            curScrollIndex = index;
            switchFunc?.Invoke(index);
            var targetX = GetTargetX(index);
            if (needScrollAnim)
            {
                _scrollRect.content.DOAnchorPos(new Vector2(-targetX, 0), 0.3f);
            }
            else
            {
                _scrollRect.content.anchoredPosition = new Vector2(-targetX, 0);
            }
        }
        
        #endregion
    }
}