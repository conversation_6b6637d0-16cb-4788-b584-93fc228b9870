using DG.Tweening;
using Game.Hotfix;
using UnityEngine;

namespace Game.Hotfix
{
    public class MarchPanel : MonoBehaviour
    {
        private bool m_IsFolding = false;

        private UIButton m_btnFold;
        private UIText m_txtTitle;
        private UIText m_txtTitleCnt;
        private UIImage m_imgFold;

        private RectTransform m_rectContent;
        private Transform m_transContainer;
        private Transform m_transTemplate;

        public void Init()
        {
            m_btnFold = transform.Find("btnTopTitle").GetComponent<UIButton>();
            m_txtTitle = transform.Find("btnTopTitle/txtTitle").GetComponent<UIText>();
            m_txtTitleCnt = transform.Find("btnTopTitle/txtTitleCnt").GetComponent<UIText>();
            m_imgFold = transform.Find("btnTopTitle/imgFold").GetComponent<UIImage>();

            m_rectContent = transform.Find("mask/team").GetComponent<RectTransform>();
            m_transContainer = transform.Find("mask/team").GetComponent<Transform>();
            m_transTemplate = transform.Find("hidden/teamTemplate").GetComponent<Transform>();


            m_btnFold.onClick.RemoveAllListeners();
            m_btnFold.onClick.AddListener(OnBtnFoldClick);
        }

        private void OnBtnFoldClick()
        {
            ChangeFoldState(!m_IsFolding);
        }

        public void OnOpen()
        {
            ResetUI();
        }

        public void OnClose()
        {
        }


        public void ResetUI()
        {
            var marchData = GameEntry.LogicData.MarchData;

            bool needShowBuyTeam = !GameEntry.LogicData.BuildingData.GetTeamIsUnlockByIndex(3);

            var dataList = marchData.GetMyDataList();
            if (needShowBuyTeam)
            {
                var data = GameEntry.LogicData.MarchData.GetEmptyWrapper();
                dataList.Add(data);
            }

            ToolScriptExtend.RecycleOrCreate(m_transTemplate.gameObject, m_transContainer, dataList.Count);
            for (var i = 0; i < dataList.Count; i++)
            {
                var child = m_transContainer.GetChild(i);
                var item = child.GetComponent<MarchPanelItem>();
                item?.SetData(dataList[i]);
            }

            if (m_IsFolding)
            {
                m_rectContent.DOAnchorPos(new Vector2(m_rectContent.anchoredPosition.x, m_rectContent.sizeDelta.y),
                    0);
            }
        }

        public void ChangeFoldState(bool folding,float duration = 0.3f)
        {
            
            
            if (m_IsFolding == folding) return;
            m_IsFolding = folding;
            if (m_IsFolding)
            {
                //收起
                m_imgFold.transform.DOLocalRotate(new Vector3(0, 0, -90), duration);
                m_rectContent.DOAnchorPos(new Vector2(m_rectContent.anchoredPosition.x, m_rectContent.sizeDelta.y),
                    duration);
            }
            else
            {
                //展开
                m_imgFold.transform.DOLocalRotate(Vector3.zero, duration);
                m_rectContent.DOAnchorPos(new Vector2(m_rectContent.anchoredPosition.x, 0),
                    duration);
            }
        }
    }
    
}