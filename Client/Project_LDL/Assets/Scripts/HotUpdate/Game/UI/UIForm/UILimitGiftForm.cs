using System;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using Gift;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UILimitGiftForm : UGuiFormEx
    {
        private ScrollNodePage _ScrollNodePage;

        private int sumCount;
        private int curIndex;
        
        private List<GiftTrigger> giftList;
        private List<TimerNode> timeList;
        private bool IsTimer = false;//是否开始倒计时逻辑
        private int curTagIndex = -1;
        private float tagWidth;
        
        private MallData MallManager => GameEntry.LogicData.MallData;
        private LimitGiftData LimitGiftManager => GameEntry.LogicData.LimitGiftData;

        private HorizontalLayoutGroup tagLayout;
        private class TimerNode
        {
            public int time;
            public UIText txt;
        }
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            timeList = new List<TimerNode>();
            m_goPrefab.SetActive(false);
            giftList = new List<GiftTrigger>();
            _ScrollNodePage = new ScrollNodePage();
            tagLayout = m_scrollviewTag.content.GetComponent<HorizontalLayoutGroup>();
            tagWidth =  m_goTag.GetComponent<RectTransform>().rect.width;
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            if (giftList == null)
            {
                giftList = new List<GiftTrigger>();
            }
            else
            {
                giftList.Clear();
            }
            
            _ScrollNodePage.OnInit(m_scrollviewList,m_goGiftItem,UpdateItemLogic,SwitchPageLogic);
            InitPageView();

            Timers.Instance.Add(GetInstanceID().ToString(), 1, (a) =>
            {
                OnTimer();
            }, 86400);
        }

        private void InitPageView()
        {
            IsTimer = false;
            giftList.Clear();
            timeList.Clear();
            var list = LimitGiftManager.GetGiftList();
            if (list.Count == 0)
            {
                Close();
                return;
            }
            giftList.AddRange(list);
            sumCount = giftList.Count;
            curIndex = 0;
            curTagIndex = -1;
            CheckBtnActive();
            _ScrollNodePage.UpdateChildCount(sumCount);
            ShowTagList();
            OnSelectTagIndex(0);
            IsTimer = true;
        }
        
        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            Timers.Instance.Remove(GetInstanceID().ToString());
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var flag = userData.ToString();
            var data = flag.Split("|");
            if (data.Length == 2)
            {
                var key = data[0];
                var value = ulong.Parse(data[1]);
                if (key == "Refresh")
                {
                    InitPageView();
                }
            }
        }

        //计时器逻辑
        public void OnTimer()
        {
            if (IsTimer)
            {
                if(timeList.Count == 0) return;
                foreach (var node in timeList)
                {
                    if (node.time > 0)
                    {
                        node.time--;
                        node.txt.text = ToolScriptExtend.FormatTime(node.time);
                    }
                    else
                    {
                        //有礼包截止了，需要重新请求信息
                        IsTimer = false;
                        break;
                    }
                }
            }
        }

        private void SwitchPageLogic(int _curIndex)
        {
            curIndex = _curIndex;
            CheckBtnActive();
            MoveToTargetTag(_curIndex);
            ShowSelectPageStatus(_curIndex-1);
            _ScrollNodePage.UpdatePageByIndex(_curIndex);
        }
        
        private void UpdateItemLogic(int index, GameObject obj)
        {
            if (giftList.Count == 0) return;
            if (!(index >= 0 && index < giftList.Count)) return;
            var data = giftList[index];
            if (!ToolScriptExtend.GetConfigById<gift_pack>(data.Id, out var giftConfig)) return;
            
            var PayId = giftConfig.payment_id;
            var root = obj.transform.Find("node");
            var discount = root.Find("discount/Text").GetComponent<UIText>();
            var btnClose = root.Find("btnClose").GetComponent<UIButton>();
            var scroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            var title = root.Find("title").GetComponent<UIText>();
            var desc = root.Find("desc").GetComponent<UIText>();
            var buyBtn = root.Find("btn").GetComponent<UIButton>();
            var price = root.Find("btn/price").GetComponent<UIText>();
            var icon = root.Find("icon").GetComponent<UIImage>();
            var limitCount = root.Find("limitCount").GetComponent<UIText>();
            var timerTxt = root.Find("TimerObj/txt").GetComponent<UIText>();
            
            //礼包倒计时
            var time = data.ExpiredTime - TimeComponent.Now;
            var recordTime = time <= 0 ? 0 : (int)time;
            timeList.Add(new TimerNode(){time = recordTime,txt = timerTxt});
            timerTxt.text = ToolScriptExtend.FormatTime(recordTime);
            
            icon.SetImage(giftConfig.gift_pack_icon);
            scroll.normalizedPosition = new Vector2(0, 1);
            ToolScriptExtend.BindBtnLogic(btnClose, () => { Close(); });
            // 可购买次数：
            var limitStr = giftConfig.buy_limit_times - data.BuyTimes;
            if (limitStr < 0)
            {
                limitStr = 0;
            }
            
            limitCount.text = $"{ToolScriptExtend.GetLang(1100176)} {limitStr}";
            price.text = MallManager.GetPrice(PayId);
            ToolScriptExtend.BindBtnLogic(buyBtn, () =>
            {
                GameEntry.PaymentData.Pay(PayId);
            });
            MallManager.CreateRechargeScore(PayId,buyBtn.transform);
            title.text = ToolScriptExtend.GetLang(giftConfig.gift_pack_name);
            desc.text = ToolScriptExtend.GetLang(giftConfig.gift_pack_desc);
            discount.text = $"{giftConfig.cost_effectiveness}%";
            var rewardRoot = scroll.content.transform;
            var rewardList = MallManager.GetRewardList(data.Id);
            ToolScriptExtend.RecycleOrCreate(m_goGiftDescItem,rewardRoot,rewardList.Count);
            for (var i = 0; i < rewardList.Count; i++)
            {
                var child = rewardRoot.GetChild(i);
                var reward = rewardList[i];
                var node = child.Find("node");
                var descTxt = child.Find("desc").GetComponent<UIText>();
                var numTxt = child.Find("num").GetComponent<UIText>();
                    
                ToolScriptExtend.LoadReward(node,reward,null,0.6f);
                descTxt.text = ToolScriptExtend.GetItemName(reward.item_id);
                numTxt.text = reward.num.ToString();
            }
        }

        #region 左右切页按钮

        private void OnBtnLeftClick()
        {
            _ScrollNodePage.CheckLeftPage();
        }
        
        private void OnBtnRightClick()
        {
             _ScrollNodePage.CheckRightPage();
        }
        
        //按钮显隐逻辑
        private void CheckBtnActive()
        {
            if (sumCount <= 1)
            {
                m_btnLeft.gameObject.SetActive(false);
                m_btnRight.gameObject.SetActive(false);
            }
            else
            {
                m_btnLeft.gameObject.SetActive(curIndex > 1);
                m_btnRight.gameObject.SetActive(curIndex <= sumCount-1);
            }
        }
        #endregion
        
        #region 底部标签

        private void ShowTagList()
        {
            AutoFitTagContent(sumCount);
            var tagRoot = m_scrollviewTag.content.transform;
            ToolScriptExtend.RecycleOrCreate(m_goTag,tagRoot,sumCount);
            for (var i = 0; i < sumCount; i++)
            {
                var child = tagRoot.GetChild(i);
                UpdateTagLogic(i, child.gameObject);
            }
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_scrollviewTag.content);
        }
        
        private void UpdateTagLogic(int index, GameObject obj)
        {
            if (giftList.Count == 0) return;
            if (!(index >= 0 && index < giftList.Count)) return;
            var data = giftList[index];
            if (!ToolScriptExtend.GetConfigById<gift_pack>(data.Id, out var giftConfig)) return;
            
            var root  = obj.transform;
            var icon = root.Find("icon").GetComponent<UIImage>();
            var btn =  root.Find("btn").GetComponent<UIButton>();
            var txt =  root.Find("txt").GetComponent<UIText>();
            
            icon.SetImage(giftConfig.gift_tab_icon);
            txt.text = ToolScriptExtend.GetLang(giftConfig.gift_pack_name);
            ToolScriptExtend.BindBtnLogic(btn, () =>
            {
                OnSelectTagIndex(index);
            });
        }

        //选择标签
        private void OnSelectTagIndex(int index)
        {
            if (sumCount <= 0) return;
            if (index == curTagIndex) return;
            ShowSelectPageStatus(index);
            _ScrollNodePage.MoveToPageByIndex(curTagIndex+1,true);
        }

        private void ShowSelectPageStatus(int index)
        {
            var tagRoot = m_scrollviewTag.content.transform;
            var childCount = tagRoot.childCount;
            for (var i = 0; i < childCount; i++)
            {
                var child = tagRoot.GetChild(i);
                var selected = child.Find("selected");
                selected.gameObject.SetActive(i == index);
            }
            curTagIndex = index;
        }
        
        private void AutoFitTagContent(int count)
        {
            var widthSum = tagWidth * count + tagLayout.spacing * (count - 1)+tagLayout.padding.left+tagLayout.padding.right;
            m_scrollviewTag.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,widthSum);
        }
        
        private void MoveToTargetTag(int index)
        {
            var contentWidth = m_scrollviewTag.content.rect.width;
            var viewWidth = m_scrollviewTag.viewport.rect.width;
            if (contentWidth <= viewWidth) return;
            
            if (index == 1)
            {
                m_scrollviewTag.content.DOAnchorPos(new Vector2(0, 0), 0.3f);
                return;
            }

            var widthSum = tagWidth * (index - 1) + tagLayout.spacing * (index - 2);
            if (widthSum < 0)
            {
                widthSum = 0;
            }
            widthSum += tagLayout.padding.left;
            var deadline = contentWidth - viewWidth;
            if (deadline < 0)
            {
                deadline = 0;
            }
            
            if (widthSum > deadline)
            {
                widthSum = deadline;
            }

            var middleOffset = 320;
            var result = -widthSum;
            if (index != sumCount)
            {
                result += middleOffset;
            }
            
            m_scrollviewTag.content.DOAnchorPos(new Vector2(result, 0), 0.3f);
        }
        
        #endregion
    }
}