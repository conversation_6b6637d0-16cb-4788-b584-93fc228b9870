// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: march.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace March {

  /// <summary>Holder for reflection information generated from march.proto</summary>
  public static partial class MarchReflection {

    #region Descriptor
    /// <summary>File descriptor for march.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MarchReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CgttYXJjaC5wcm90bxIFbWFyY2gaEGdhbWVjb25maWcucHJvdG8igwQKCU1h",
            "cmNoSW5mbxIQCghtYXJjaF9pZBgBIAEoBBIuCgptYXJjaF90eXBlGAIgASgO",
            "MhoucGJfZ2FtZWNvbmZpZy5tYXJjaF90eXBlcxITCgtmb3JtX21hcF9pZBgD",
            "IAEoBRIPCgdyb2xlX2lkGAQgASgEEhEKCXRvX21hcF9pZBgFIAEoBRISCgp0",
            "b19yb2xlX2lkGAYgASgEEiAKBXN0YXRlGAcgASgOMhEubWFyY2guTWFyY2hT",
            "dGF0ZRISCgpzdGFydF90aW1lGAggASgDEhAKCGVuZF90aW1lGAkgASgDEhAK",
            "CHVuaW9uX2lkGAogASgEEhMKC3RvX3VuaW9uX2lkGAsgASgEEhEKCXJvbGVf",
            "bmFtZRgMIAEoCRIXCg9yb2xlX3VuaW9uX25hbWUYDSABKAkSEQoJc2VydmVy",
            "X2lkGA4gASgNEhUKDWNvbGxlY3Rfc3BlZWQYDyABKAESEwoLdG9fbWFyY2hf",
            "aWQYECABKAQSOAoLdG9fbWFwX3R5cGUYESABKA4yIy5wYl9nYW1lY29uZmln",
            "Lm1hcF9lbGVtZW50X2JpZ190eXBlEhUKDWJhc2VfZW5kX3RpbWUYEiABKAMS",
            "EwoLY29sbGVjdF9udW0YEyABKAMSJwoIaGVyb19pZHMYFCADKA4yFS5wYl9n",
            "YW1lY29uZmlnLml0ZW1pZCKnAQoOTWFyY2hDcmVhdGVSZXESDgoGbWFwX2lk",
            "GAEgASgFEi4KCm1hcmNoX3R5cGUYAiABKA4yGi5wYl9nYW1lY29uZmlnLm1h",
            "cmNoX3R5cGVzEicKCGhlcm9faWRzGAMgAygOMhUucGJfZ2FtZWNvbmZpZy5p",
            "dGVtaWQSFgoOZGVmZW5kX3JvbGVfaWQYBCABKAQSFAoMdG9fc2VydmVyX2lk",
            "GAUgASgNIqQCCg9NYXJjaEJhY2tBcnJpdmUSDwoHcm9sZV9pZBgBIAEoBBIn",
            "CghoZXJvX2lkcxgCIAMoDjIVLnBiX2dhbWVjb25maWcuaXRlbWlkEhAKCG1h",
            "cmNoX2lkGAMgASgEEi4KCm1hcmNoX3R5cGUYBCABKA4yGi5wYl9nYW1lY29u",
            "ZmlnLm1hcmNoX3R5cGVzEhAKCHBoeXNpY2FsGAUgASgFEjUKCG1hcF90eXBl",
            "GAYgASgOMiMucGJfZ2FtZWNvbmZpZy5tYXBfZWxlbWVudF9iaWdfdHlwZRIT",
            "Cgtjb2xsZWN0X251bRgHIAEoAxIUCgxjb2xsZWN0X3RpbWUYCCABKAMSDgoG",
            "bWFwX2lkGAkgASgFEhEKCW1hcF9sZXZlbBgKIAEoBSLgAQoTV29ybGRNYXJj",
            "aENyZWF0ZVJlcRIrCgxjbGllbnRfcGFyYW0YASABKAsyFS5tYXJjaC5NYXJj",
            "aENyZWF0ZVJlcRIOCgZtYXBfaWQYAiABKAUSEQoJc2VydmVyX2lkGAMgASgN",
            "EhUKDWNvbGxlY3Rfc3BlZWQYBCABKAESGAoQY29sbGVjdF9jYXBhY2l0eRgF",
            "IAEoAxIWCg5zcGVlZF9hZGRpdGlvbhgGIAEoDRIQCghwaHlzaWNhbBgHIAEo",
            "BRIQCgh1bmlvbl9pZBgIIAEoBBIMCgRsb2FkGAkgASgNIjoKFFdvcmxkTWFy",
            "Y2hDcmVhdGVSZXNwEhAKCG1hcmNoX2lkGAEgASgEEhAKCGVuZF90aW1lGAIg",
            "ASgDInMKFVdvcmxkTWFyY2hDYWxsQmFja1JlcRIQCghtYXJjaF9pZBgBIAEo",
            "BBISCgpvcF9yb2xlX2lkGAIgASgEEjQKEGNhbGxfYmFja19hY3Rpb24YAyAB",
            "KA4yGi5tYXJjaC5NYXJjaENhbGxCYWNrQWN0aW9uIlIKFldvcmxkTWFyY2hD",
            "YWxsQmFja1Jlc3ASOAoLdG9fbWFwX3R5cGUYASABKA4yIy5wYl9nYW1lY29u",
            "ZmlnLm1hcF9lbGVtZW50X2JpZ190eXBlIlQKGFdvcmxkTWFyY2hDYWxsQmFj",
            "a05vd1JlcRIVCg1tYXJjaF9pZF9saXN0GAEgAygEEg8KB3JvbGVfaWQYAiAB",
            "KAQSEAoIdW5pb25faWQYAyABKAQiXAoPTm90aWNlTWFyY2hCYWNrEhAKCG1h",
            "cmNoX2lkGAEgASgEEiUKBmhlcm9lcxgCIAMoDjIVLnBiX2dhbWVjb25maWcu",
            "aXRlbWlkEhAKCHBoeXNpY2FsGAMgASgNKpwBCgpNYXJjaFN0YXRlEhEKDU1h",
            "cmNoU3RhdGVOaWwQABIUChBNYXJjaFN0YXRlTW92aW5nEAESEgoOTWFyY2hT",
            "dGF0ZUJhY2sQAhIVChFNYXJjaFN0YXRlU3RhdGlvbhADEhoKFk1hcmNoU3Rh",
            "dGVBc3NlbWJsZVdhaXQQBBIeChpNYXJjaFN0YXRlTW9uc3RlckF0dGFja2lu",
            "ZxAFKrABChNNYXJjaENhbGxCYWNrQWN0aW9uEh0KGU1hcmNoQ2FsbEJhY2tB",
            "Y3Rpb25Ob3JtYWwQABIeChpNYXJjaENhbGxCYWNrQWN0aW9uS2lja091dBAB",
            "Eh0KGU1hcmNoQ2FsbEJhY2tBY3Rpb25EZWxldGUQAhIeChpNYXJjaENhbGxC",
            "YWNrQWN0aW9uVGltZU91dBADEhsKF01hcmNoQ2FsbEJhY2tBY3Rpb25RdWl0",
            "EARCGFoWc2VydmVyL2FwaS9wYi9wYl9tYXJjaGIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::PbGameconfig.GameconfigReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::March.MarchState), typeof(global::March.MarchCallBackAction), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::March.MarchInfo), global::March.MarchInfo.Parser, new[]{ "MarchId", "MarchType", "FormMapId", "RoleId", "ToMapId", "ToRoleId", "State", "StartTime", "EndTime", "UnionId", "ToUnionId", "RoleName", "RoleUnionName", "ServerId", "CollectSpeed", "ToMarchId", "ToMapType", "BaseEndTime", "CollectNum", "HeroIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.MarchCreateReq), global::March.MarchCreateReq.Parser, new[]{ "MapId", "MarchType", "HeroIds", "DefendRoleId", "ToServerId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.MarchBackArrive), global::March.MarchBackArrive.Parser, new[]{ "RoleId", "HeroIds", "MarchId", "MarchType", "Physical", "MapType", "CollectNum", "CollectTime", "MapId", "MapLevel" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.WorldMarchCreateReq), global::March.WorldMarchCreateReq.Parser, new[]{ "ClientParam", "MapId", "ServerId", "CollectSpeed", "CollectCapacity", "SpeedAddition", "Physical", "UnionId", "Load" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.WorldMarchCreateResp), global::March.WorldMarchCreateResp.Parser, new[]{ "MarchId", "EndTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.WorldMarchCallBackReq), global::March.WorldMarchCallBackReq.Parser, new[]{ "MarchId", "OpRoleId", "CallBackAction" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.WorldMarchCallBackResp), global::March.WorldMarchCallBackResp.Parser, new[]{ "ToMapType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.WorldMarchCallBackNowReq), global::March.WorldMarchCallBackNowReq.Parser, new[]{ "MarchIdList", "RoleId", "UnionId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::March.NoticeMarchBack), global::March.NoticeMarchBack.Parser, new[]{ "MarchId", "Heroes", "Physical" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// MarchState 行军状态
  /// </summary>
  public enum MarchState {
    /// <summary>
    /// 0
    /// </summary>
    [pbr::OriginalName("MarchStateNil")] Nil = 0,
    /// <summary>
    /// 行军中
    /// </summary>
    [pbr::OriginalName("MarchStateMoving")] Moving = 1,
    /// <summary>
    /// 行军返回中
    /// </summary>
    [pbr::OriginalName("MarchStateBack")] Back = 2,
    /// <summary>
    /// 行军驻守中
    /// </summary>
    [pbr::OriginalName("MarchStateStation")] Station = 3,
    /// <summary>
    /// 行军集结等待中
    /// </summary>
    [pbr::OriginalName("MarchStateAssembleWait")] AssembleWait = 4,
    /// <summary>
    /// 怪物攻击中
    /// </summary>
    [pbr::OriginalName("MarchStateMonsterAttacking")] MonsterAttacking = 5,
  }

  public enum MarchCallBackAction {
    /// <summary>
    /// 行军召回正常行为
    /// </summary>
    [pbr::OriginalName("MarchCallBackActionNormal")] Normal = 0,
    /// <summary>
    /// 行军召回踢出行为
    /// </summary>
    [pbr::OriginalName("MarchCallBackActionKickOut")] KickOut = 1,
    /// <summary>
    /// 行军召回解散删除行为
    /// </summary>
    [pbr::OriginalName("MarchCallBackActionDelete")] Delete = 2,
    /// <summary>
    /// 行军召回超时(人数不足)
    /// </summary>
    [pbr::OriginalName("MarchCallBackActionTimeOut")] TimeOut = 3,
    /// <summary>
    /// 行军召回主动退出(集结行军)
    /// </summary>
    [pbr::OriginalName("MarchCallBackActionQuit")] Quit = 4,
  }

  #endregion

  #region Messages
  /// <summary>
  /// MarchInfo 行军信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class MarchInfo : pb::IMessage<MarchInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MarchInfo> _parser = new pb::MessageParser<MarchInfo>(() => new MarchInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MarchInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchInfo(MarchInfo other) : this() {
      marchId_ = other.marchId_;
      marchType_ = other.marchType_;
      formMapId_ = other.formMapId_;
      roleId_ = other.roleId_;
      toMapId_ = other.toMapId_;
      toRoleId_ = other.toRoleId_;
      state_ = other.state_;
      startTime_ = other.startTime_;
      endTime_ = other.endTime_;
      unionId_ = other.unionId_;
      toUnionId_ = other.toUnionId_;
      roleName_ = other.roleName_;
      roleUnionName_ = other.roleUnionName_;
      serverId_ = other.serverId_;
      collectSpeed_ = other.collectSpeed_;
      toMarchId_ = other.toMarchId_;
      toMapType_ = other.toMapType_;
      baseEndTime_ = other.baseEndTime_;
      collectNum_ = other.collectNum_;
      heroIds_ = other.heroIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchInfo Clone() {
      return new MarchInfo(this);
    }

    /// <summary>Field number for the "march_id" field.</summary>
    public const int MarchIdFieldNumber = 1;
    private ulong marchId_;
    /// <summary>
    /// 行军ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong MarchId {
      get { return marchId_; }
      set {
        marchId_ = value;
      }
    }

    /// <summary>Field number for the "march_type" field.</summary>
    public const int MarchTypeFieldNumber = 2;
    private global::PbGameconfig.march_types marchType_ = global::PbGameconfig.march_types._0;
    /// <summary>
    /// 行军类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.march_types MarchType {
      get { return marchType_; }
      set {
        marchType_ = value;
      }
    }

    /// <summary>Field number for the "form_map_id" field.</summary>
    public const int FormMapIdFieldNumber = 3;
    private int formMapId_;
    /// <summary>
    /// 起始地图坐标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FormMapId {
      get { return formMapId_; }
      set {
        formMapId_ = value;
      }
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 4;
    private ulong roleId_;
    /// <summary>
    /// 角色ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    /// <summary>Field number for the "to_map_id" field.</summary>
    public const int ToMapIdFieldNumber = 5;
    private int toMapId_;
    /// <summary>
    /// 目标地图坐标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ToMapId {
      get { return toMapId_; }
      set {
        toMapId_ = value;
      }
    }

    /// <summary>Field number for the "to_role_id" field.</summary>
    public const int ToRoleIdFieldNumber = 6;
    private ulong toRoleId_;
    /// <summary>
    /// 角色ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong ToRoleId {
      get { return toRoleId_; }
      set {
        toRoleId_ = value;
      }
    }

    /// <summary>Field number for the "state" field.</summary>
    public const int StateFieldNumber = 7;
    private global::March.MarchState state_ = global::March.MarchState.Nil;
    /// <summary>
    /// 行军状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::March.MarchState State {
      get { return state_; }
      set {
        state_ = value;
      }
    }

    /// <summary>Field number for the "start_time" field.</summary>
    public const int StartTimeFieldNumber = 8;
    private long startTime_;
    /// <summary>
    /// 行军开始时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StartTime {
      get { return startTime_; }
      set {
        startTime_ = value;
      }
    }

    /// <summary>Field number for the "end_time" field.</summary>
    public const int EndTimeFieldNumber = 9;
    private long endTime_;
    /// <summary>
    /// 行军结束时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long EndTime {
      get { return endTime_; }
      set {
        endTime_ = value;
      }
    }

    /// <summary>Field number for the "union_id" field.</summary>
    public const int UnionIdFieldNumber = 10;
    private ulong unionId_;
    /// <summary>
    /// 发起者联盟ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong UnionId {
      get { return unionId_; }
      set {
        unionId_ = value;
      }
    }

    /// <summary>Field number for the "to_union_id" field.</summary>
    public const int ToUnionIdFieldNumber = 11;
    private ulong toUnionId_;
    /// <summary>
    /// 目标者联盟ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong ToUnionId {
      get { return toUnionId_; }
      set {
        toUnionId_ = value;
      }
    }

    /// <summary>Field number for the "role_name" field.</summary>
    public const int RoleNameFieldNumber = 12;
    private string roleName_ = "";
    /// <summary>
    /// 角色名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string RoleName {
      get { return roleName_; }
      set {
        roleName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "role_union_name" field.</summary>
    public const int RoleUnionNameFieldNumber = 13;
    private string roleUnionName_ = "";
    /// <summary>
    /// 联盟名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string RoleUnionName {
      get { return roleUnionName_; }
      set {
        roleUnionName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 14;
    private uint serverId_;
    /// <summary>
    /// 行军者服务器ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "collect_speed" field.</summary>
    public const int CollectSpeedFieldNumber = 15;
    private double collectSpeed_;
    /// <summary>
    /// 采集速度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double CollectSpeed {
      get { return collectSpeed_; }
      set {
        collectSpeed_ = value;
      }
    }

    /// <summary>Field number for the "to_march_id" field.</summary>
    public const int ToMarchIdFieldNumber = 16;
    private ulong toMarchId_;
    /// <summary>
    /// 目标行军ID（集结使用）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong ToMarchId {
      get { return toMarchId_; }
      set {
        toMarchId_ = value;
      }
    }

    /// <summary>Field number for the "to_map_type" field.</summary>
    public const int ToMapTypeFieldNumber = 17;
    private global::PbGameconfig.map_element_big_type toMapType_ = global::PbGameconfig.map_element_big_type._0;
    /// <summary>
    /// 目标地图类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.map_element_big_type ToMapType {
      get { return toMapType_; }
      set {
        toMapType_ = value;
      }
    }

    /// <summary>Field number for the "base_end_time" field.</summary>
    public const int BaseEndTimeFieldNumber = 18;
    private long baseEndTime_;
    /// <summary>
    /// 初始行军结束时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long BaseEndTime {
      get { return baseEndTime_; }
      set {
        baseEndTime_ = value;
      }
    }

    /// <summary>Field number for the "collect_num" field.</summary>
    public const int CollectNumFieldNumber = 19;
    private long collectNum_;
    /// <summary>
    /// 采集数量(结算)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CollectNum {
      get { return collectNum_; }
      set {
        collectNum_ = value;
      }
    }

    /// <summary>Field number for the "hero_ids" field.</summary>
    public const int HeroIdsFieldNumber = 20;
    private static readonly pb::FieldCodec<global::PbGameconfig.itemid> _repeated_heroIds_codec
        = pb::FieldCodec.ForEnum(162, x => (int) x, x => (global::PbGameconfig.itemid) x);
    private readonly pbc::RepeatedField<global::PbGameconfig.itemid> heroIds_ = new pbc::RepeatedField<global::PbGameconfig.itemid>();
    /// <summary>
    /// 英雄
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.itemid> HeroIds {
      get { return heroIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MarchInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MarchInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MarchId != other.MarchId) return false;
      if (MarchType != other.MarchType) return false;
      if (FormMapId != other.FormMapId) return false;
      if (RoleId != other.RoleId) return false;
      if (ToMapId != other.ToMapId) return false;
      if (ToRoleId != other.ToRoleId) return false;
      if (State != other.State) return false;
      if (StartTime != other.StartTime) return false;
      if (EndTime != other.EndTime) return false;
      if (UnionId != other.UnionId) return false;
      if (ToUnionId != other.ToUnionId) return false;
      if (RoleName != other.RoleName) return false;
      if (RoleUnionName != other.RoleUnionName) return false;
      if (ServerId != other.ServerId) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(CollectSpeed, other.CollectSpeed)) return false;
      if (ToMarchId != other.ToMarchId) return false;
      if (ToMapType != other.ToMapType) return false;
      if (BaseEndTime != other.BaseEndTime) return false;
      if (CollectNum != other.CollectNum) return false;
      if(!heroIds_.Equals(other.heroIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MarchId != 0UL) hash ^= MarchId.GetHashCode();
      if (MarchType != global::PbGameconfig.march_types._0) hash ^= MarchType.GetHashCode();
      if (FormMapId != 0) hash ^= FormMapId.GetHashCode();
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      if (ToMapId != 0) hash ^= ToMapId.GetHashCode();
      if (ToRoleId != 0UL) hash ^= ToRoleId.GetHashCode();
      if (State != global::March.MarchState.Nil) hash ^= State.GetHashCode();
      if (StartTime != 0L) hash ^= StartTime.GetHashCode();
      if (EndTime != 0L) hash ^= EndTime.GetHashCode();
      if (UnionId != 0UL) hash ^= UnionId.GetHashCode();
      if (ToUnionId != 0UL) hash ^= ToUnionId.GetHashCode();
      if (RoleName.Length != 0) hash ^= RoleName.GetHashCode();
      if (RoleUnionName.Length != 0) hash ^= RoleUnionName.GetHashCode();
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (CollectSpeed != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(CollectSpeed);
      if (ToMarchId != 0UL) hash ^= ToMarchId.GetHashCode();
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) hash ^= ToMapType.GetHashCode();
      if (BaseEndTime != 0L) hash ^= BaseEndTime.GetHashCode();
      if (CollectNum != 0L) hash ^= CollectNum.GetHashCode();
      hash ^= heroIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) MarchType);
      }
      if (FormMapId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(FormMapId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(RoleId);
      }
      if (ToMapId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(ToMapId);
      }
      if (ToRoleId != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(ToRoleId);
      }
      if (State != global::March.MarchState.Nil) {
        output.WriteRawTag(56);
        output.WriteEnum((int) State);
      }
      if (StartTime != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(StartTime);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(EndTime);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(80);
        output.WriteUInt64(UnionId);
      }
      if (ToUnionId != 0UL) {
        output.WriteRawTag(88);
        output.WriteUInt64(ToUnionId);
      }
      if (RoleName.Length != 0) {
        output.WriteRawTag(98);
        output.WriteString(RoleName);
      }
      if (RoleUnionName.Length != 0) {
        output.WriteRawTag(106);
        output.WriteString(RoleUnionName);
      }
      if (ServerId != 0) {
        output.WriteRawTag(112);
        output.WriteUInt32(ServerId);
      }
      if (CollectSpeed != 0D) {
        output.WriteRawTag(121);
        output.WriteDouble(CollectSpeed);
      }
      if (ToMarchId != 0UL) {
        output.WriteRawTag(128, 1);
        output.WriteUInt64(ToMarchId);
      }
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) {
        output.WriteRawTag(136, 1);
        output.WriteEnum((int) ToMapType);
      }
      if (BaseEndTime != 0L) {
        output.WriteRawTag(144, 1);
        output.WriteInt64(BaseEndTime);
      }
      if (CollectNum != 0L) {
        output.WriteRawTag(152, 1);
        output.WriteInt64(CollectNum);
      }
      heroIds_.WriteTo(output, _repeated_heroIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) MarchType);
      }
      if (FormMapId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(FormMapId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(RoleId);
      }
      if (ToMapId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(ToMapId);
      }
      if (ToRoleId != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(ToRoleId);
      }
      if (State != global::March.MarchState.Nil) {
        output.WriteRawTag(56);
        output.WriteEnum((int) State);
      }
      if (StartTime != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(StartTime);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(EndTime);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(80);
        output.WriteUInt64(UnionId);
      }
      if (ToUnionId != 0UL) {
        output.WriteRawTag(88);
        output.WriteUInt64(ToUnionId);
      }
      if (RoleName.Length != 0) {
        output.WriteRawTag(98);
        output.WriteString(RoleName);
      }
      if (RoleUnionName.Length != 0) {
        output.WriteRawTag(106);
        output.WriteString(RoleUnionName);
      }
      if (ServerId != 0) {
        output.WriteRawTag(112);
        output.WriteUInt32(ServerId);
      }
      if (CollectSpeed != 0D) {
        output.WriteRawTag(121);
        output.WriteDouble(CollectSpeed);
      }
      if (ToMarchId != 0UL) {
        output.WriteRawTag(128, 1);
        output.WriteUInt64(ToMarchId);
      }
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) {
        output.WriteRawTag(136, 1);
        output.WriteEnum((int) ToMapType);
      }
      if (BaseEndTime != 0L) {
        output.WriteRawTag(144, 1);
        output.WriteInt64(BaseEndTime);
      }
      if (CollectNum != 0L) {
        output.WriteRawTag(152, 1);
        output.WriteInt64(CollectNum);
      }
      heroIds_.WriteTo(ref output, _repeated_heroIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MarchId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(MarchId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) MarchType);
      }
      if (FormMapId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(FormMapId);
      }
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      if (ToMapId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ToMapId);
      }
      if (ToRoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(ToRoleId);
      }
      if (State != global::March.MarchState.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) State);
      }
      if (StartTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StartTime);
      }
      if (EndTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(EndTime);
      }
      if (UnionId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(UnionId);
      }
      if (ToUnionId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(ToUnionId);
      }
      if (RoleName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(RoleName);
      }
      if (RoleUnionName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(RoleUnionName);
      }
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (CollectSpeed != 0D) {
        size += 1 + 8;
      }
      if (ToMarchId != 0UL) {
        size += 2 + pb::CodedOutputStream.ComputeUInt64Size(ToMarchId);
      }
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) {
        size += 2 + pb::CodedOutputStream.ComputeEnumSize((int) ToMapType);
      }
      if (BaseEndTime != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(BaseEndTime);
      }
      if (CollectNum != 0L) {
        size += 2 + pb::CodedOutputStream.ComputeInt64Size(CollectNum);
      }
      size += heroIds_.CalculateSize(_repeated_heroIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MarchInfo other) {
      if (other == null) {
        return;
      }
      if (other.MarchId != 0UL) {
        MarchId = other.MarchId;
      }
      if (other.MarchType != global::PbGameconfig.march_types._0) {
        MarchType = other.MarchType;
      }
      if (other.FormMapId != 0) {
        FormMapId = other.FormMapId;
      }
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      if (other.ToMapId != 0) {
        ToMapId = other.ToMapId;
      }
      if (other.ToRoleId != 0UL) {
        ToRoleId = other.ToRoleId;
      }
      if (other.State != global::March.MarchState.Nil) {
        State = other.State;
      }
      if (other.StartTime != 0L) {
        StartTime = other.StartTime;
      }
      if (other.EndTime != 0L) {
        EndTime = other.EndTime;
      }
      if (other.UnionId != 0UL) {
        UnionId = other.UnionId;
      }
      if (other.ToUnionId != 0UL) {
        ToUnionId = other.ToUnionId;
      }
      if (other.RoleName.Length != 0) {
        RoleName = other.RoleName;
      }
      if (other.RoleUnionName.Length != 0) {
        RoleUnionName = other.RoleUnionName;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.CollectSpeed != 0D) {
        CollectSpeed = other.CollectSpeed;
      }
      if (other.ToMarchId != 0UL) {
        ToMarchId = other.ToMarchId;
      }
      if (other.ToMapType != global::PbGameconfig.map_element_big_type._0) {
        ToMapType = other.ToMapType;
      }
      if (other.BaseEndTime != 0L) {
        BaseEndTime = other.BaseEndTime;
      }
      if (other.CollectNum != 0L) {
        CollectNum = other.CollectNum;
      }
      heroIds_.Add(other.heroIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 16: {
            MarchType = (global::PbGameconfig.march_types) input.ReadEnum();
            break;
          }
          case 24: {
            FormMapId = input.ReadInt32();
            break;
          }
          case 32: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 40: {
            ToMapId = input.ReadInt32();
            break;
          }
          case 48: {
            ToRoleId = input.ReadUInt64();
            break;
          }
          case 56: {
            State = (global::March.MarchState) input.ReadEnum();
            break;
          }
          case 64: {
            StartTime = input.ReadInt64();
            break;
          }
          case 72: {
            EndTime = input.ReadInt64();
            break;
          }
          case 80: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 88: {
            ToUnionId = input.ReadUInt64();
            break;
          }
          case 98: {
            RoleName = input.ReadString();
            break;
          }
          case 106: {
            RoleUnionName = input.ReadString();
            break;
          }
          case 112: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 121: {
            CollectSpeed = input.ReadDouble();
            break;
          }
          case 128: {
            ToMarchId = input.ReadUInt64();
            break;
          }
          case 136: {
            ToMapType = (global::PbGameconfig.map_element_big_type) input.ReadEnum();
            break;
          }
          case 144: {
            BaseEndTime = input.ReadInt64();
            break;
          }
          case 152: {
            CollectNum = input.ReadInt64();
            break;
          }
          case 162:
          case 160: {
            heroIds_.AddEntriesFrom(input, _repeated_heroIds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 16: {
            MarchType = (global::PbGameconfig.march_types) input.ReadEnum();
            break;
          }
          case 24: {
            FormMapId = input.ReadInt32();
            break;
          }
          case 32: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 40: {
            ToMapId = input.ReadInt32();
            break;
          }
          case 48: {
            ToRoleId = input.ReadUInt64();
            break;
          }
          case 56: {
            State = (global::March.MarchState) input.ReadEnum();
            break;
          }
          case 64: {
            StartTime = input.ReadInt64();
            break;
          }
          case 72: {
            EndTime = input.ReadInt64();
            break;
          }
          case 80: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 88: {
            ToUnionId = input.ReadUInt64();
            break;
          }
          case 98: {
            RoleName = input.ReadString();
            break;
          }
          case 106: {
            RoleUnionName = input.ReadString();
            break;
          }
          case 112: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 121: {
            CollectSpeed = input.ReadDouble();
            break;
          }
          case 128: {
            ToMarchId = input.ReadUInt64();
            break;
          }
          case 136: {
            ToMapType = (global::PbGameconfig.map_element_big_type) input.ReadEnum();
            break;
          }
          case 144: {
            BaseEndTime = input.ReadInt64();
            break;
          }
          case 152: {
            CollectNum = input.ReadInt64();
            break;
          }
          case 162:
          case 160: {
            heroIds_.AddEntriesFrom(ref input, _repeated_heroIds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class MarchCreateReq : pb::IMessage<MarchCreateReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MarchCreateReq> _parser = new pb::MessageParser<MarchCreateReq>(() => new MarchCreateReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MarchCreateReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchCreateReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchCreateReq(MarchCreateReq other) : this() {
      mapId_ = other.mapId_;
      marchType_ = other.marchType_;
      heroIds_ = other.heroIds_.Clone();
      defendRoleId_ = other.defendRoleId_;
      toServerId_ = other.toServerId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchCreateReq Clone() {
      return new MarchCreateReq(this);
    }

    /// <summary>Field number for the "map_id" field.</summary>
    public const int MapIdFieldNumber = 1;
    private int mapId_;
    /// <summary>
    /// 目标位置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MapId {
      get { return mapId_; }
      set {
        mapId_ = value;
      }
    }

    /// <summary>Field number for the "march_type" field.</summary>
    public const int MarchTypeFieldNumber = 2;
    private global::PbGameconfig.march_types marchType_ = global::PbGameconfig.march_types._0;
    /// <summary>
    /// 行军类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.march_types MarchType {
      get { return marchType_; }
      set {
        marchType_ = value;
      }
    }

    /// <summary>Field number for the "hero_ids" field.</summary>
    public const int HeroIdsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::PbGameconfig.itemid> _repeated_heroIds_codec
        = pb::FieldCodec.ForEnum(26, x => (int) x, x => (global::PbGameconfig.itemid) x);
    private readonly pbc::RepeatedField<global::PbGameconfig.itemid> heroIds_ = new pbc::RepeatedField<global::PbGameconfig.itemid>();
    /// <summary>
    /// 英雄ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.itemid> HeroIds {
      get { return heroIds_; }
    }

    /// <summary>Field number for the "defend_role_id" field.</summary>
    public const int DefendRoleIdFieldNumber = 4;
    private ulong defendRoleId_;
    /// <summary>
    /// 防守方角色ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DefendRoleId {
      get { return defendRoleId_; }
      set {
        defendRoleId_ = value;
      }
    }

    /// <summary>Field number for the "to_server_id" field.</summary>
    public const int ToServerIdFieldNumber = 5;
    private uint toServerId_;
    /// <summary>
    /// 目标服务器ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ToServerId {
      get { return toServerId_; }
      set {
        toServerId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MarchCreateReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MarchCreateReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MapId != other.MapId) return false;
      if (MarchType != other.MarchType) return false;
      if(!heroIds_.Equals(other.heroIds_)) return false;
      if (DefendRoleId != other.DefendRoleId) return false;
      if (ToServerId != other.ToServerId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MapId != 0) hash ^= MapId.GetHashCode();
      if (MarchType != global::PbGameconfig.march_types._0) hash ^= MarchType.GetHashCode();
      hash ^= heroIds_.GetHashCode();
      if (DefendRoleId != 0UL) hash ^= DefendRoleId.GetHashCode();
      if (ToServerId != 0) hash ^= ToServerId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MapId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MapId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) MarchType);
      }
      heroIds_.WriteTo(output, _repeated_heroIds_codec);
      if (DefendRoleId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(DefendRoleId);
      }
      if (ToServerId != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(ToServerId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MapId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MapId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) MarchType);
      }
      heroIds_.WriteTo(ref output, _repeated_heroIds_codec);
      if (DefendRoleId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(DefendRoleId);
      }
      if (ToServerId != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(ToServerId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MapId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MapId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) MarchType);
      }
      size += heroIds_.CalculateSize(_repeated_heroIds_codec);
      if (DefendRoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(DefendRoleId);
      }
      if (ToServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ToServerId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MarchCreateReq other) {
      if (other == null) {
        return;
      }
      if (other.MapId != 0) {
        MapId = other.MapId;
      }
      if (other.MarchType != global::PbGameconfig.march_types._0) {
        MarchType = other.MarchType;
      }
      heroIds_.Add(other.heroIds_);
      if (other.DefendRoleId != 0UL) {
        DefendRoleId = other.DefendRoleId;
      }
      if (other.ToServerId != 0) {
        ToServerId = other.ToServerId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MapId = input.ReadInt32();
            break;
          }
          case 16: {
            MarchType = (global::PbGameconfig.march_types) input.ReadEnum();
            break;
          }
          case 26:
          case 24: {
            heroIds_.AddEntriesFrom(input, _repeated_heroIds_codec);
            break;
          }
          case 32: {
            DefendRoleId = input.ReadUInt64();
            break;
          }
          case 40: {
            ToServerId = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MapId = input.ReadInt32();
            break;
          }
          case 16: {
            MarchType = (global::PbGameconfig.march_types) input.ReadEnum();
            break;
          }
          case 26:
          case 24: {
            heroIds_.AddEntriesFrom(ref input, _repeated_heroIds_codec);
            break;
          }
          case 32: {
            DefendRoleId = input.ReadUInt64();
            break;
          }
          case 40: {
            ToServerId = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// MarchBackArrive 行军返回到达
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class MarchBackArrive : pb::IMessage<MarchBackArrive>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MarchBackArrive> _parser = new pb::MessageParser<MarchBackArrive>(() => new MarchBackArrive());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MarchBackArrive> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchBackArrive() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchBackArrive(MarchBackArrive other) : this() {
      roleId_ = other.roleId_;
      heroIds_ = other.heroIds_.Clone();
      marchId_ = other.marchId_;
      marchType_ = other.marchType_;
      physical_ = other.physical_;
      mapType_ = other.mapType_;
      collectNum_ = other.collectNum_;
      collectTime_ = other.collectTime_;
      mapId_ = other.mapId_;
      mapLevel_ = other.mapLevel_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MarchBackArrive Clone() {
      return new MarchBackArrive(this);
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 1;
    private ulong roleId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    /// <summary>Field number for the "hero_ids" field.</summary>
    public const int HeroIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::PbGameconfig.itemid> _repeated_heroIds_codec
        = pb::FieldCodec.ForEnum(18, x => (int) x, x => (global::PbGameconfig.itemid) x);
    private readonly pbc::RepeatedField<global::PbGameconfig.itemid> heroIds_ = new pbc::RepeatedField<global::PbGameconfig.itemid>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.itemid> HeroIds {
      get { return heroIds_; }
    }

    /// <summary>Field number for the "march_id" field.</summary>
    public const int MarchIdFieldNumber = 3;
    private ulong marchId_;
    /// <summary>
    /// 这里传当前回到的行军ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong MarchId {
      get { return marchId_; }
      set {
        marchId_ = value;
      }
    }

    /// <summary>Field number for the "march_type" field.</summary>
    public const int MarchTypeFieldNumber = 4;
    private global::PbGameconfig.march_types marchType_ = global::PbGameconfig.march_types._0;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.march_types MarchType {
      get { return marchType_; }
      set {
        marchType_ = value;
      }
    }

    /// <summary>Field number for the "physical" field.</summary>
    public const int PhysicalFieldNumber = 5;
    private int physical_;
    /// <summary>
    /// 体力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Physical {
      get { return physical_; }
      set {
        physical_ = value;
      }
    }

    /// <summary>Field number for the "map_type" field.</summary>
    public const int MapTypeFieldNumber = 6;
    private global::PbGameconfig.map_element_big_type mapType_ = global::PbGameconfig.map_element_big_type._0;
    /// <summary>
    /// 地图类型 采集的地图类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.map_element_big_type MapType {
      get { return mapType_; }
      set {
        mapType_ = value;
      }
    }

    /// <summary>Field number for the "collect_num" field.</summary>
    public const int CollectNumFieldNumber = 7;
    private long collectNum_;
    /// <summary>
    /// 采集量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CollectNum {
      get { return collectNum_; }
      set {
        collectNum_ = value;
      }
    }

    /// <summary>Field number for the "collect_time" field.</summary>
    public const int CollectTimeFieldNumber = 8;
    private long collectTime_;
    /// <summary>
    /// 时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CollectTime {
      get { return collectTime_; }
      set {
        collectTime_ = value;
      }
    }

    /// <summary>Field number for the "map_id" field.</summary>
    public const int MapIdFieldNumber = 9;
    private int mapId_;
    /// <summary>
    /// 位置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MapId {
      get { return mapId_; }
      set {
        mapId_ = value;
      }
    }

    /// <summary>Field number for the "map_level" field.</summary>
    public const int MapLevelFieldNumber = 10;
    private int mapLevel_;
    /// <summary>
    /// 等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MapLevel {
      get { return mapLevel_; }
      set {
        mapLevel_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MarchBackArrive);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MarchBackArrive other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (RoleId != other.RoleId) return false;
      if(!heroIds_.Equals(other.heroIds_)) return false;
      if (MarchId != other.MarchId) return false;
      if (MarchType != other.MarchType) return false;
      if (Physical != other.Physical) return false;
      if (MapType != other.MapType) return false;
      if (CollectNum != other.CollectNum) return false;
      if (CollectTime != other.CollectTime) return false;
      if (MapId != other.MapId) return false;
      if (MapLevel != other.MapLevel) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      hash ^= heroIds_.GetHashCode();
      if (MarchId != 0UL) hash ^= MarchId.GetHashCode();
      if (MarchType != global::PbGameconfig.march_types._0) hash ^= MarchType.GetHashCode();
      if (Physical != 0) hash ^= Physical.GetHashCode();
      if (MapType != global::PbGameconfig.map_element_big_type._0) hash ^= MapType.GetHashCode();
      if (CollectNum != 0L) hash ^= CollectNum.GetHashCode();
      if (CollectTime != 0L) hash ^= CollectTime.GetHashCode();
      if (MapId != 0) hash ^= MapId.GetHashCode();
      if (MapLevel != 0) hash ^= MapLevel.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (RoleId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(RoleId);
      }
      heroIds_.WriteTo(output, _repeated_heroIds_codec);
      if (MarchId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(MarchId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        output.WriteRawTag(32);
        output.WriteEnum((int) MarchType);
      }
      if (Physical != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Physical);
      }
      if (MapType != global::PbGameconfig.map_element_big_type._0) {
        output.WriteRawTag(48);
        output.WriteEnum((int) MapType);
      }
      if (CollectNum != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(CollectNum);
      }
      if (CollectTime != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(CollectTime);
      }
      if (MapId != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(MapId);
      }
      if (MapLevel != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(MapLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (RoleId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(RoleId);
      }
      heroIds_.WriteTo(ref output, _repeated_heroIds_codec);
      if (MarchId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(MarchId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        output.WriteRawTag(32);
        output.WriteEnum((int) MarchType);
      }
      if (Physical != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Physical);
      }
      if (MapType != global::PbGameconfig.map_element_big_type._0) {
        output.WriteRawTag(48);
        output.WriteEnum((int) MapType);
      }
      if (CollectNum != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(CollectNum);
      }
      if (CollectTime != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(CollectTime);
      }
      if (MapId != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(MapId);
      }
      if (MapLevel != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(MapLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      size += heroIds_.CalculateSize(_repeated_heroIds_codec);
      if (MarchId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(MarchId);
      }
      if (MarchType != global::PbGameconfig.march_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) MarchType);
      }
      if (Physical != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Physical);
      }
      if (MapType != global::PbGameconfig.map_element_big_type._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) MapType);
      }
      if (CollectNum != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CollectNum);
      }
      if (CollectTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CollectTime);
      }
      if (MapId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MapId);
      }
      if (MapLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MapLevel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MarchBackArrive other) {
      if (other == null) {
        return;
      }
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      heroIds_.Add(other.heroIds_);
      if (other.MarchId != 0UL) {
        MarchId = other.MarchId;
      }
      if (other.MarchType != global::PbGameconfig.march_types._0) {
        MarchType = other.MarchType;
      }
      if (other.Physical != 0) {
        Physical = other.Physical;
      }
      if (other.MapType != global::PbGameconfig.map_element_big_type._0) {
        MapType = other.MapType;
      }
      if (other.CollectNum != 0L) {
        CollectNum = other.CollectNum;
      }
      if (other.CollectTime != 0L) {
        CollectTime = other.CollectTime;
      }
      if (other.MapId != 0) {
        MapId = other.MapId;
      }
      if (other.MapLevel != 0) {
        MapLevel = other.MapLevel;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            heroIds_.AddEntriesFrom(input, _repeated_heroIds_codec);
            break;
          }
          case 24: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 32: {
            MarchType = (global::PbGameconfig.march_types) input.ReadEnum();
            break;
          }
          case 40: {
            Physical = input.ReadInt32();
            break;
          }
          case 48: {
            MapType = (global::PbGameconfig.map_element_big_type) input.ReadEnum();
            break;
          }
          case 56: {
            CollectNum = input.ReadInt64();
            break;
          }
          case 64: {
            CollectTime = input.ReadInt64();
            break;
          }
          case 72: {
            MapId = input.ReadInt32();
            break;
          }
          case 80: {
            MapLevel = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            heroIds_.AddEntriesFrom(ref input, _repeated_heroIds_codec);
            break;
          }
          case 24: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 32: {
            MarchType = (global::PbGameconfig.march_types) input.ReadEnum();
            break;
          }
          case 40: {
            Physical = input.ReadInt32();
            break;
          }
          case 48: {
            MapType = (global::PbGameconfig.map_element_big_type) input.ReadEnum();
            break;
          }
          case 56: {
            CollectNum = input.ReadInt64();
            break;
          }
          case 64: {
            CollectTime = input.ReadInt64();
            break;
          }
          case 72: {
            MapId = input.ReadInt32();
            break;
          }
          case 80: {
            MapLevel = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 创建行军 (内部接口) 44403
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorldMarchCreateReq : pb::IMessage<WorldMarchCreateReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorldMarchCreateReq> _parser = new pb::MessageParser<WorldMarchCreateReq>(() => new WorldMarchCreateReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorldMarchCreateReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCreateReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCreateReq(WorldMarchCreateReq other) : this() {
      clientParam_ = other.clientParam_ != null ? other.clientParam_.Clone() : null;
      mapId_ = other.mapId_;
      serverId_ = other.serverId_;
      collectSpeed_ = other.collectSpeed_;
      collectCapacity_ = other.collectCapacity_;
      speedAddition_ = other.speedAddition_;
      physical_ = other.physical_;
      unionId_ = other.unionId_;
      load_ = other.load_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCreateReq Clone() {
      return new WorldMarchCreateReq(this);
    }

    /// <summary>Field number for the "client_param" field.</summary>
    public const int ClientParamFieldNumber = 1;
    private global::March.MarchCreateReq clientParam_;
    /// <summary>
    /// 前端参数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::March.MarchCreateReq ClientParam {
      get { return clientParam_; }
      set {
        clientParam_ = value;
      }
    }

    /// <summary>Field number for the "map_id" field.</summary>
    public const int MapIdFieldNumber = 2;
    private int mapId_;
    /// <summary>
    /// 超始点坐标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MapId {
      get { return mapId_; }
      set {
        mapId_ = value;
      }
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 3;
    private uint serverId_;
    /// <summary>
    /// 发起者服务器ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "collect_speed" field.</summary>
    public const int CollectSpeedFieldNumber = 4;
    private double collectSpeed_;
    /// <summary>
    /// 采集速度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double CollectSpeed {
      get { return collectSpeed_; }
      set {
        collectSpeed_ = value;
      }
    }

    /// <summary>Field number for the "collect_capacity" field.</summary>
    public const int CollectCapacityFieldNumber = 5;
    private long collectCapacity_;
    /// <summary>
    /// 本次行军可以采集的量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CollectCapacity {
      get { return collectCapacity_; }
      set {
        collectCapacity_ = value;
      }
    }

    /// <summary>Field number for the "speed_addition" field.</summary>
    public const int SpeedAdditionFieldNumber = 6;
    private uint speedAddition_;
    /// <summary>
    /// 行军速度加成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint SpeedAddition {
      get { return speedAddition_; }
      set {
        speedAddition_ = value;
      }
    }

    /// <summary>Field number for the "physical" field.</summary>
    public const int PhysicalFieldNumber = 7;
    private int physical_;
    /// <summary>
    /// 体力消耗
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Physical {
      get { return physical_; }
      set {
        physical_ = value;
      }
    }

    /// <summary>Field number for the "union_id" field.</summary>
    public const int UnionIdFieldNumber = 8;
    private ulong unionId_;
    /// <summary>
    /// 联盟ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong UnionId {
      get { return unionId_; }
      set {
        unionId_ = value;
      }
    }

    /// <summary>Field number for the "load" field.</summary>
    public const int LoadFieldNumber = 9;
    private uint load_;
    /// <summary>
    /// 本次行军负重
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Load {
      get { return load_; }
      set {
        load_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorldMarchCreateReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorldMarchCreateReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(ClientParam, other.ClientParam)) return false;
      if (MapId != other.MapId) return false;
      if (ServerId != other.ServerId) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(CollectSpeed, other.CollectSpeed)) return false;
      if (CollectCapacity != other.CollectCapacity) return false;
      if (SpeedAddition != other.SpeedAddition) return false;
      if (Physical != other.Physical) return false;
      if (UnionId != other.UnionId) return false;
      if (Load != other.Load) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (clientParam_ != null) hash ^= ClientParam.GetHashCode();
      if (MapId != 0) hash ^= MapId.GetHashCode();
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (CollectSpeed != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(CollectSpeed);
      if (CollectCapacity != 0L) hash ^= CollectCapacity.GetHashCode();
      if (SpeedAddition != 0) hash ^= SpeedAddition.GetHashCode();
      if (Physical != 0) hash ^= Physical.GetHashCode();
      if (UnionId != 0UL) hash ^= UnionId.GetHashCode();
      if (Load != 0) hash ^= Load.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (clientParam_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(ClientParam);
      }
      if (MapId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MapId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (CollectSpeed != 0D) {
        output.WriteRawTag(33);
        output.WriteDouble(CollectSpeed);
      }
      if (CollectCapacity != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(CollectCapacity);
      }
      if (SpeedAddition != 0) {
        output.WriteRawTag(48);
        output.WriteUInt32(SpeedAddition);
      }
      if (Physical != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(Physical);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(64);
        output.WriteUInt64(UnionId);
      }
      if (Load != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(Load);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (clientParam_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(ClientParam);
      }
      if (MapId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MapId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (CollectSpeed != 0D) {
        output.WriteRawTag(33);
        output.WriteDouble(CollectSpeed);
      }
      if (CollectCapacity != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(CollectCapacity);
      }
      if (SpeedAddition != 0) {
        output.WriteRawTag(48);
        output.WriteUInt32(SpeedAddition);
      }
      if (Physical != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(Physical);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(64);
        output.WriteUInt64(UnionId);
      }
      if (Load != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(Load);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (clientParam_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ClientParam);
      }
      if (MapId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MapId);
      }
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (CollectSpeed != 0D) {
        size += 1 + 8;
      }
      if (CollectCapacity != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CollectCapacity);
      }
      if (SpeedAddition != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(SpeedAddition);
      }
      if (Physical != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Physical);
      }
      if (UnionId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(UnionId);
      }
      if (Load != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Load);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorldMarchCreateReq other) {
      if (other == null) {
        return;
      }
      if (other.clientParam_ != null) {
        if (clientParam_ == null) {
          ClientParam = new global::March.MarchCreateReq();
        }
        ClientParam.MergeFrom(other.ClientParam);
      }
      if (other.MapId != 0) {
        MapId = other.MapId;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.CollectSpeed != 0D) {
        CollectSpeed = other.CollectSpeed;
      }
      if (other.CollectCapacity != 0L) {
        CollectCapacity = other.CollectCapacity;
      }
      if (other.SpeedAddition != 0) {
        SpeedAddition = other.SpeedAddition;
      }
      if (other.Physical != 0) {
        Physical = other.Physical;
      }
      if (other.UnionId != 0UL) {
        UnionId = other.UnionId;
      }
      if (other.Load != 0) {
        Load = other.Load;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (clientParam_ == null) {
              ClientParam = new global::March.MarchCreateReq();
            }
            input.ReadMessage(ClientParam);
            break;
          }
          case 16: {
            MapId = input.ReadInt32();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 33: {
            CollectSpeed = input.ReadDouble();
            break;
          }
          case 40: {
            CollectCapacity = input.ReadInt64();
            break;
          }
          case 48: {
            SpeedAddition = input.ReadUInt32();
            break;
          }
          case 56: {
            Physical = input.ReadInt32();
            break;
          }
          case 64: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 72: {
            Load = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (clientParam_ == null) {
              ClientParam = new global::March.MarchCreateReq();
            }
            input.ReadMessage(ClientParam);
            break;
          }
          case 16: {
            MapId = input.ReadInt32();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 33: {
            CollectSpeed = input.ReadDouble();
            break;
          }
          case 40: {
            CollectCapacity = input.ReadInt64();
            break;
          }
          case 48: {
            SpeedAddition = input.ReadUInt32();
            break;
          }
          case 56: {
            Physical = input.ReadInt32();
            break;
          }
          case 64: {
            UnionId = input.ReadUInt64();
            break;
          }
          case 72: {
            Load = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorldMarchCreateResp : pb::IMessage<WorldMarchCreateResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorldMarchCreateResp> _parser = new pb::MessageParser<WorldMarchCreateResp>(() => new WorldMarchCreateResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorldMarchCreateResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCreateResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCreateResp(WorldMarchCreateResp other) : this() {
      marchId_ = other.marchId_;
      endTime_ = other.endTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCreateResp Clone() {
      return new WorldMarchCreateResp(this);
    }

    /// <summary>Field number for the "march_id" field.</summary>
    public const int MarchIdFieldNumber = 1;
    private ulong marchId_;
    /// <summary>
    /// 行军ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong MarchId {
      get { return marchId_; }
      set {
        marchId_ = value;
      }
    }

    /// <summary>Field number for the "end_time" field.</summary>
    public const int EndTimeFieldNumber = 2;
    private long endTime_;
    /// <summary>
    /// 行军结束时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long EndTime {
      get { return endTime_; }
      set {
        endTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorldMarchCreateResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorldMarchCreateResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MarchId != other.MarchId) return false;
      if (EndTime != other.EndTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MarchId != 0UL) hash ^= MarchId.GetHashCode();
      if (EndTime != 0L) hash ^= EndTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(EndTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(EndTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MarchId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(MarchId);
      }
      if (EndTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(EndTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorldMarchCreateResp other) {
      if (other == null) {
        return;
      }
      if (other.MarchId != 0UL) {
        MarchId = other.MarchId;
      }
      if (other.EndTime != 0L) {
        EndTime = other.EndTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 16: {
            EndTime = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 16: {
            EndTime = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 召回行军 (内部接口) 44404
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorldMarchCallBackReq : pb::IMessage<WorldMarchCallBackReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorldMarchCallBackReq> _parser = new pb::MessageParser<WorldMarchCallBackReq>(() => new WorldMarchCallBackReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorldMarchCallBackReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackReq(WorldMarchCallBackReq other) : this() {
      marchId_ = other.marchId_;
      opRoleId_ = other.opRoleId_;
      callBackAction_ = other.callBackAction_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackReq Clone() {
      return new WorldMarchCallBackReq(this);
    }

    /// <summary>Field number for the "march_id" field.</summary>
    public const int MarchIdFieldNumber = 1;
    private ulong marchId_;
    /// <summary>
    /// 行军ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong MarchId {
      get { return marchId_; }
      set {
        marchId_ = value;
      }
    }

    /// <summary>Field number for the "op_role_id" field.</summary>
    public const int OpRoleIdFieldNumber = 2;
    private ulong opRoleId_;
    /// <summary>
    /// 操作角色ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong OpRoleId {
      get { return opRoleId_; }
      set {
        opRoleId_ = value;
      }
    }

    /// <summary>Field number for the "call_back_action" field.</summary>
    public const int CallBackActionFieldNumber = 3;
    private global::March.MarchCallBackAction callBackAction_ = global::March.MarchCallBackAction.Normal;
    /// <summary>
    /// 召回行为类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::March.MarchCallBackAction CallBackAction {
      get { return callBackAction_; }
      set {
        callBackAction_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorldMarchCallBackReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorldMarchCallBackReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MarchId != other.MarchId) return false;
      if (OpRoleId != other.OpRoleId) return false;
      if (CallBackAction != other.CallBackAction) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MarchId != 0UL) hash ^= MarchId.GetHashCode();
      if (OpRoleId != 0UL) hash ^= OpRoleId.GetHashCode();
      if (CallBackAction != global::March.MarchCallBackAction.Normal) hash ^= CallBackAction.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      if (OpRoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(OpRoleId);
      }
      if (CallBackAction != global::March.MarchCallBackAction.Normal) {
        output.WriteRawTag(24);
        output.WriteEnum((int) CallBackAction);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      if (OpRoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(OpRoleId);
      }
      if (CallBackAction != global::March.MarchCallBackAction.Normal) {
        output.WriteRawTag(24);
        output.WriteEnum((int) CallBackAction);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MarchId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(MarchId);
      }
      if (OpRoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(OpRoleId);
      }
      if (CallBackAction != global::March.MarchCallBackAction.Normal) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) CallBackAction);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorldMarchCallBackReq other) {
      if (other == null) {
        return;
      }
      if (other.MarchId != 0UL) {
        MarchId = other.MarchId;
      }
      if (other.OpRoleId != 0UL) {
        OpRoleId = other.OpRoleId;
      }
      if (other.CallBackAction != global::March.MarchCallBackAction.Normal) {
        CallBackAction = other.CallBackAction;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 16: {
            OpRoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            CallBackAction = (global::March.MarchCallBackAction) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 16: {
            OpRoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            CallBackAction = (global::March.MarchCallBackAction) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorldMarchCallBackResp : pb::IMessage<WorldMarchCallBackResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorldMarchCallBackResp> _parser = new pb::MessageParser<WorldMarchCallBackResp>(() => new WorldMarchCallBackResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorldMarchCallBackResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackResp(WorldMarchCallBackResp other) : this() {
      toMapType_ = other.toMapType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackResp Clone() {
      return new WorldMarchCallBackResp(this);
    }

    /// <summary>Field number for the "to_map_type" field.</summary>
    public const int ToMapTypeFieldNumber = 1;
    private global::PbGameconfig.map_element_big_type toMapType_ = global::PbGameconfig.map_element_big_type._0;
    /// <summary>
    /// 地图类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.map_element_big_type ToMapType {
      get { return toMapType_; }
      set {
        toMapType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorldMarchCallBackResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorldMarchCallBackResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ToMapType != other.ToMapType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) hash ^= ToMapType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) ToMapType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) ToMapType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ToMapType != global::PbGameconfig.map_element_big_type._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) ToMapType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorldMarchCallBackResp other) {
      if (other == null) {
        return;
      }
      if (other.ToMapType != global::PbGameconfig.map_element_big_type._0) {
        ToMapType = other.ToMapType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ToMapType = (global::PbGameconfig.map_element_big_type) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ToMapType = (global::PbGameconfig.map_element_big_type) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 立即召回行军 (内部接口) 44405
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorldMarchCallBackNowReq : pb::IMessage<WorldMarchCallBackNowReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorldMarchCallBackNowReq> _parser = new pb::MessageParser<WorldMarchCallBackNowReq>(() => new WorldMarchCallBackNowReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorldMarchCallBackNowReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackNowReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackNowReq(WorldMarchCallBackNowReq other) : this() {
      marchIdList_ = other.marchIdList_.Clone();
      roleId_ = other.roleId_;
      unionId_ = other.unionId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorldMarchCallBackNowReq Clone() {
      return new WorldMarchCallBackNowReq(this);
    }

    /// <summary>Field number for the "march_id_list" field.</summary>
    public const int MarchIdListFieldNumber = 1;
    private static readonly pb::FieldCodec<ulong> _repeated_marchIdList_codec
        = pb::FieldCodec.ForUInt64(10);
    private readonly pbc::RepeatedField<ulong> marchIdList_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 行军ID列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> MarchIdList {
      get { return marchIdList_; }
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 2;
    private ulong roleId_;
    /// <summary>
    /// 角色ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    /// <summary>Field number for the "union_id" field.</summary>
    public const int UnionIdFieldNumber = 3;
    private ulong unionId_;
    /// <summary>
    /// 联盟ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong UnionId {
      get { return unionId_; }
      set {
        unionId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorldMarchCallBackNowReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorldMarchCallBackNowReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!marchIdList_.Equals(other.marchIdList_)) return false;
      if (RoleId != other.RoleId) return false;
      if (UnionId != other.UnionId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= marchIdList_.GetHashCode();
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      if (UnionId != 0UL) hash ^= UnionId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      marchIdList_.WriteTo(output, _repeated_marchIdList_codec);
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(UnionId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      marchIdList_.WriteTo(ref output, _repeated_marchIdList_codec);
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (UnionId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(UnionId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += marchIdList_.CalculateSize(_repeated_marchIdList_codec);
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      if (UnionId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(UnionId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorldMarchCallBackNowReq other) {
      if (other == null) {
        return;
      }
      marchIdList_.Add(other.marchIdList_);
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      if (other.UnionId != 0UL) {
        UnionId = other.UnionId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            marchIdList_.AddEntriesFrom(input, _repeated_marchIdList_codec);
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            UnionId = input.ReadUInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10:
          case 8: {
            marchIdList_.AddEntriesFrom(ref input, _repeated_marchIdList_codec);
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            UnionId = input.ReadUInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// NoticeMarchBack 行军回来推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NoticeMarchBack : pb::IMessage<NoticeMarchBack>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NoticeMarchBack> _parser = new pb::MessageParser<NoticeMarchBack>(() => new NoticeMarchBack());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NoticeMarchBack> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::March.MarchReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NoticeMarchBack() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NoticeMarchBack(NoticeMarchBack other) : this() {
      marchId_ = other.marchId_;
      heroes_ = other.heroes_.Clone();
      physical_ = other.physical_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NoticeMarchBack Clone() {
      return new NoticeMarchBack(this);
    }

    /// <summary>Field number for the "march_id" field.</summary>
    public const int MarchIdFieldNumber = 1;
    private ulong marchId_;
    /// <summary>
    /// 行军ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong MarchId {
      get { return marchId_; }
      set {
        marchId_ = value;
      }
    }

    /// <summary>Field number for the "heroes" field.</summary>
    public const int HeroesFieldNumber = 2;
    private static readonly pb::FieldCodec<global::PbGameconfig.itemid> _repeated_heroes_codec
        = pb::FieldCodec.ForEnum(18, x => (int) x, x => (global::PbGameconfig.itemid) x);
    private readonly pbc::RepeatedField<global::PbGameconfig.itemid> heroes_ = new pbc::RepeatedField<global::PbGameconfig.itemid>();
    /// <summary>
    /// 英雄数据(可有可无)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.itemid> Heroes {
      get { return heroes_; }
    }

    /// <summary>Field number for the "physical" field.</summary>
    public const int PhysicalFieldNumber = 3;
    private uint physical_;
    /// <summary>
    /// 体力(可有可无)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Physical {
      get { return physical_; }
      set {
        physical_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NoticeMarchBack);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NoticeMarchBack other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MarchId != other.MarchId) return false;
      if(!heroes_.Equals(other.heroes_)) return false;
      if (Physical != other.Physical) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MarchId != 0UL) hash ^= MarchId.GetHashCode();
      hash ^= heroes_.GetHashCode();
      if (Physical != 0) hash ^= Physical.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      heroes_.WriteTo(output, _repeated_heroes_codec);
      if (Physical != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Physical);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MarchId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MarchId);
      }
      heroes_.WriteTo(ref output, _repeated_heroes_codec);
      if (Physical != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Physical);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MarchId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(MarchId);
      }
      size += heroes_.CalculateSize(_repeated_heroes_codec);
      if (Physical != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Physical);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NoticeMarchBack other) {
      if (other == null) {
        return;
      }
      if (other.MarchId != 0UL) {
        MarchId = other.MarchId;
      }
      heroes_.Add(other.heroes_);
      if (other.Physical != 0) {
        Physical = other.Physical;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            heroes_.AddEntriesFrom(input, _repeated_heroes_codec);
            break;
          }
          case 24: {
            Physical = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MarchId = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            heroes_.AddEntriesFrom(ref input, _repeated_heroes_codec);
            break;
          }
          case 24: {
            Physical = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
