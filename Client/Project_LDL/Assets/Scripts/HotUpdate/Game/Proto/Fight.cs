// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: fight.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Fight {

  /// <summary>Holder for reflection information generated from fight.proto</summary>
  public static partial class FightReflection {

    #region Descriptor
    /// <summary>File descriptor for fight.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static FightReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CgtmaWdodC5wcm90bxIFZmlnaHQaDGJhdHRsZS5wcm90bxoMY29tbW9uLnBy",
            "b3RvGhBnYW1lY29uZmlnLnByb3RvGgp0ZWFtLnByb3RvIjQKDVRlYW1Nb2Rp",
            "ZnlSZXESIwoFdGVhbXMYASADKAsyFC5maWdodC5Gb3JtYXRpb25UZWFtIjUK",
            "DlRlYW1Nb2RpZnlSZXNwEiMKBXRlYW1zGAEgAygLMhQuZmlnaHQuRm9ybWF0",
            "aW9uVGVhbSJWCgxUZWFtUXVlcnlSZXESIgoKdGVhbV90eXBlcxgBIAMoDjIO",
            "LnRlYW0uVGVhbVR5cGUSDwoHcm9sZV9pZBgCIAEoBBIRCglzZXJ2ZXJfaWQY",
            "AyABKA0iVAoNVGVhbVF1ZXJ5UmVzcBIfCgV0ZWFtcxgBIAMoCzIQLmZpZ2h0",
            "LlF1ZXJ5VGVhbRIPCgdyb2xlX2lkGAIgASgEEhEKCXNlcnZlcl9pZBgDIAEo",
            "DSLIAQoOQmF0dGxlRGVidWdSZXESIgoIYXR0YWNrZXIYASABKAsyEC5maWdo",
            "dC5EZWJ1Z1RlYW0SIgoIZGVmZW5kZXIYAiABKAsyEC5maWdodC5EZWJ1Z1Rl",
            "YW0SDQoFbGV2ZWwYAyABKAUSEgoKc3Rhcl9zdGFnZRgEIAEoBRITCgtza2ls",
            "bF9sZXZlbBgFIAEoBRIQCghocF90aW1lcxgGIAEoBRIkCgRhcmdzGAcgASgL",
            "MhYuYmF0dGxlLkJhdHRsZVB1cmVBcmdzIiQKD0JhdHRsZURlYnVnUmVzcBIR",
            "CglyZXBvcnRfaWQYASABKAkiNwoTVGVhbURlZmVuZE1vZGlmeVJlcRIgCgV0",
            "ZWFtcxgBIAMoCzIRLmZpZ2h0LkRlZmVuZFRlYW0iOAoUVGVhbURlZmVuZE1v",
            "ZGlmeVJlc3ASIAoFdGVhbXMYASADKAsyES5maWdodC5EZWZlbmRUZWFtIrQC",
            "ChpCYXR0bGVDcmVhdGVCeUZvcm1hdGlvblJlcRIwCgtiYXR0bGVfdHlwZRgB",
            "IAEoDjIbLnBiX2dhbWVjb25maWcuYmF0dGxlX3R5cGVzEhgKEGF0dGFja2Vy",
            "X3JvbGVfaWQYAiABKAQSGAoQZGVmZW5kZXJfcm9sZV9pZBgDIAEoBBIrChNh",
            "dHRhY2tlcl90ZWFtX3R5cGVzGAQgAygOMg4udGVhbS5UZWFtVHlwZRIrChNk",
            "ZWZlbmRlcl90ZWFtX3R5cGVzGAUgAygOMg4udGVhbS5UZWFtVHlwZRIrCgps",
            "b2dpY19hcmdzGAYgASgLMhcuYmF0dGxlLkJhdHRsZUxvZ2ljQXJncxIpCglw",
            "dXJlX2FyZ3MYByABKAsyFi5iYXR0bGUuQmF0dGxlUHVyZUFyZ3MiSQobQmF0",
            "dGxlQ3JlYXRlQnlGb3JtYXRpb25SZXNwEioKB3Jlc3VsdHMYASADKAsyGS5m",
            "aWdodC5CYXR0bGVDcmVhdGVSZXN1bHQicAoSQmF0dGxlVGVhbVF1ZXJ5UmVx",
            "EiIKCnRlYW1fdHlwZXMYASADKA4yDi50ZWFtLlRlYW1UeXBlEhkKEWlzX2F1",
            "dG9fZm9ybWF0aW9uGAIgASgIEhsKE2lzX2FsbG93X3RlYW1fZW1wdHkYAyAB",
            "KAgiMgoTQmF0dGxlVGVhbVF1ZXJ5UmVzcBIbCgV0ZWFtcxgBIAMoCzIMLmJh",
            "dHRsZS5UZWFtIlYKDlB1c2hUZWFtU3RhdHVzEiEKCXRlYW1fdHlwZRgBIAEo",
            "DjIOLnRlYW0uVGVhbVR5cGUSIQoGc3RhdHVzGAIgASgOMhEuZmlnaHQuVGVh",
            "bVN0YXR1cyI4ChFQdXNoRm9ybWF0aW9uVGVhbRIjCgV0ZWFtcxgBIAMoCzIU",
            "LmZpZ2h0LkZvcm1hdGlvblRlYW0iMgoOUHVzaERlZmVuZFRlYW0SIAoFdGVh",
            "bXMYASADKAsyES5maWdodC5EZWZlbmRUZWFtIocCChhCYXR0bGVDcmVhdGVC",
            "eU1vbnN0ZXJSZXESMAoLYmF0dGxlX3R5cGUYASABKA4yGy5wYl9nYW1lY29u",
            "ZmlnLmJhdHRsZV90eXBlcxIYChBhdHRhY2tlcl9yb2xlX2lkGAIgASgEEioK",
            "EmF0dGFja2VyX3RlYW1fdHlwZRgDIAEoDjIOLnRlYW0uVGVhbVR5cGUSGwoT",
            "ZGVmZW5kZXJfbW9uc3Rlcl9pZBgEIAEoBRIrCgpsb2dpY19hcmdzGAUgASgL",
            "MhcuYmF0dGxlLkJhdHRsZUxvZ2ljQXJncxIpCglwdXJlX2FyZ3MYBiABKAsy",
            "Fi5iYXR0bGUuQmF0dGxlUHVyZUFyZ3MiRgoZQmF0dGxlQ3JlYXRlQnlNb25z",
            "dGVyUmVzcBIpCgZyZXN1bHQYASABKAsyGS5maWdodC5CYXR0bGVDcmVhdGVS",
            "ZXN1bHQiOwoWVGVhbVF1ZXJ5UG93ZXJCdWlsZFJlcRIhCgl0ZWFtX3R5cGUY",
            "ASABKA4yDi50ZWFtLlRlYW1UeXBlIkEKF1RlYW1RdWVyeVBvd2VyQnVpbGRS",
            "ZXNwEiYKBmJ1aWxkcxgBIAMoCzIWLmNvbW1vbi5UZWFtUG93ZXJCdWlsZCIt",
            "CglEZWJ1Z1RlYW0SIAoGaGVyb2VzGAEgAygLMhAuZmlnaHQuRGVidWdIZXJv",
            "Ij0KCURlYnVnSGVybxIjCgRjb2RlGAEgASgOMhUucGJfZ2FtZWNvbmZpZy5p",
            "dGVtaWQSCwoDcG9zGAIgASgFIooBCg1Gb3JtYXRpb25UZWFtEiEKCXRlYW1f",
            "dHlwZRgBIAEoDjIOLnRlYW0uVGVhbVR5cGUSJAoGaGVyb2VzGAIgAygLMhQu",
            "ZmlnaHQuRm9ybWF0aW9uSGVybxIhCgZzdGF0dXMYAyABKA4yES5maWdodC5U",
            "ZWFtU3RhdHVzEg0KBXBvd2VyGAQgASgDIkQKDUZvcm1hdGlvbkhlcm8SJgoH",
            "aGVyb19pZBgBIAEoDjIVLnBiX2dhbWVjb25maWcuaXRlbWlkEgsKA3BvcxgC",
            "IAEoBSJjCglRdWVyeVRlYW0SIQoJdGVhbV90eXBlGAEgASgOMg4udGVhbS5U",
            "ZWFtVHlwZRIkCgZoZXJvZXMYAiADKAsyFC5maWdodC5RdWVyeVRlYW1IZXJv",
            "Eg0KBXBvd2VyGAMgASgDInYKDVF1ZXJ5VGVhbUhlcm8SJgoHaGVyb19pZBgB",
            "IAEoDjIVLnBiX2dhbWVjb25maWcuaXRlbWlkEgsKA3BvcxgCIAEoBRINCgVs",
            "ZXZlbBgDIAEoDRISCgpzdGFyX3N0YWdlGAQgASgNEg0KBXBvd2VyGAUgASgE",
            "Ij4KCkRlZmVuZFRlYW0SDQoFaW5kZXgYASABKAUSIQoJdGVhbV90eXBlGAIg",
            "ASgOMg4udGVhbS5UZWFtVHlwZSI3ChJCYXR0bGVDcmVhdGVSZXN1bHQSEQoJ",
            "cmVwb3J0X2lkGAEgASgJEg4KBnJlc3VsdBgCIAEoBSpOCgpUZWFtU3RhdHVz",
            "EgwKCEdhcnJpc29uEAASCgoGQmF0dGxlEAESCwoHQ29sbGVjdBACEg0KCUdh",
            "dGhlcmluZxADEgoKBk1vdmluZxAEQhhaFnNlcnZlci9hcGkvcGIvcGJfZmln",
            "aHRiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Battle.BattleReflection.Descriptor, global::Common.CommonReflection.Descriptor, global::PbGameconfig.GameconfigReflection.Descriptor, global::Team.TeamReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Fight.TeamStatus), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamModifyReq), global::Fight.TeamModifyReq.Parser, new[]{ "Teams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamModifyResp), global::Fight.TeamModifyResp.Parser, new[]{ "Teams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamQueryReq), global::Fight.TeamQueryReq.Parser, new[]{ "TeamTypes", "RoleId", "ServerId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamQueryResp), global::Fight.TeamQueryResp.Parser, new[]{ "Teams", "RoleId", "ServerId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleDebugReq), global::Fight.BattleDebugReq.Parser, new[]{ "Attacker", "Defender", "Level", "StarStage", "SkillLevel", "HpTimes", "Args" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleDebugResp), global::Fight.BattleDebugResp.Parser, new[]{ "ReportId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamDefendModifyReq), global::Fight.TeamDefendModifyReq.Parser, new[]{ "Teams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamDefendModifyResp), global::Fight.TeamDefendModifyResp.Parser, new[]{ "Teams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleCreateByFormationReq), global::Fight.BattleCreateByFormationReq.Parser, new[]{ "BattleType", "AttackerRoleId", "DefenderRoleId", "AttackerTeamTypes", "DefenderTeamTypes", "LogicArgs", "PureArgs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleCreateByFormationResp), global::Fight.BattleCreateByFormationResp.Parser, new[]{ "Results" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleTeamQueryReq), global::Fight.BattleTeamQueryReq.Parser, new[]{ "TeamTypes", "IsAutoFormation", "IsAllowTeamEmpty" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleTeamQueryResp), global::Fight.BattleTeamQueryResp.Parser, new[]{ "Teams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.PushTeamStatus), global::Fight.PushTeamStatus.Parser, new[]{ "TeamType", "Status" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.PushFormationTeam), global::Fight.PushFormationTeam.Parser, new[]{ "Teams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.PushDefendTeam), global::Fight.PushDefendTeam.Parser, new[]{ "Teams" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleCreateByMonsterReq), global::Fight.BattleCreateByMonsterReq.Parser, new[]{ "BattleType", "AttackerRoleId", "AttackerTeamType", "DefenderMonsterId", "LogicArgs", "PureArgs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleCreateByMonsterResp), global::Fight.BattleCreateByMonsterResp.Parser, new[]{ "Result" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamQueryPowerBuildReq), global::Fight.TeamQueryPowerBuildReq.Parser, new[]{ "TeamType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.TeamQueryPowerBuildResp), global::Fight.TeamQueryPowerBuildResp.Parser, new[]{ "Builds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.DebugTeam), global::Fight.DebugTeam.Parser, new[]{ "Heroes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.DebugHero), global::Fight.DebugHero.Parser, new[]{ "Code", "Pos" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.FormationTeam), global::Fight.FormationTeam.Parser, new[]{ "TeamType", "Heroes", "Status", "Power" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.FormationHero), global::Fight.FormationHero.Parser, new[]{ "HeroId", "Pos" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.QueryTeam), global::Fight.QueryTeam.Parser, new[]{ "TeamType", "Heroes", "Power" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.QueryTeamHero), global::Fight.QueryTeamHero.Parser, new[]{ "HeroId", "Pos", "Level", "StarStage", "Power" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.DefendTeam), global::Fight.DefendTeam.Parser, new[]{ "Index", "TeamType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fight.BattleCreateResult), global::Fight.BattleCreateResult.Parser, new[]{ "ReportId", "Result" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum TeamStatus {
    /// <summary>
    /// 驻守
    /// </summary>
    [pbr::OriginalName("Garrison")] Garrison = 0,
    /// <summary>
    /// 外出作战
    /// </summary>
    [pbr::OriginalName("Battle")] Battle = 1,
    /// <summary>
    /// 外出采集
    /// </summary>
    [pbr::OriginalName("Collect")] Collect = 2,
    /// <summary>
    /// 集结中
    /// </summary>
    [pbr::OriginalName("Gathering")] Gathering = 3,
    /// <summary>
    /// 出征/归家
    /// </summary>
    [pbr::OriginalName("Moving")] Moving = 4,
  }

  #endregion

  #region Messages
  /// <summary>
  /// 布阵修改 3301
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamModifyReq : pb::IMessage<TeamModifyReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamModifyReq> _parser = new pb::MessageParser<TeamModifyReq>(() => new TeamModifyReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamModifyReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamModifyReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamModifyReq(TeamModifyReq other) : this() {
      teams_ = other.teams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamModifyReq Clone() {
      return new TeamModifyReq(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.FormationTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.FormationTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.FormationTeam> teams_ = new pbc::RepeatedField<global::Fight.FormationTeam>();
    /// <summary>
    /// 布阵队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.FormationTeam> Teams {
      get { return teams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamModifyReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamModifyReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamModifyReq other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamModifyResp : pb::IMessage<TeamModifyResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamModifyResp> _parser = new pb::MessageParser<TeamModifyResp>(() => new TeamModifyResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamModifyResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamModifyResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamModifyResp(TeamModifyResp other) : this() {
      teams_ = other.teams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamModifyResp Clone() {
      return new TeamModifyResp(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.FormationTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.FormationTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.FormationTeam> teams_ = new pbc::RepeatedField<global::Fight.FormationTeam>();
    /// <summary>
    /// 布阵队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.FormationTeam> Teams {
      get { return teams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamModifyResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamModifyResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamModifyResp other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 阵容查询 3302
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamQueryReq : pb::IMessage<TeamQueryReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamQueryReq> _parser = new pb::MessageParser<TeamQueryReq>(() => new TeamQueryReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamQueryReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryReq(TeamQueryReq other) : this() {
      teamTypes_ = other.teamTypes_.Clone();
      roleId_ = other.roleId_;
      serverId_ = other.serverId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryReq Clone() {
      return new TeamQueryReq(this);
    }

    /// <summary>Field number for the "team_types" field.</summary>
    public const int TeamTypesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Team.TeamType> _repeated_teamTypes_codec
        = pb::FieldCodec.ForEnum(10, x => (int) x, x => (global::Team.TeamType) x);
    private readonly pbc::RepeatedField<global::Team.TeamType> teamTypes_ = new pbc::RepeatedField<global::Team.TeamType>();
    /// <summary>
    /// 队伍类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Team.TeamType> TeamTypes {
      get { return teamTypes_; }
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 2;
    private ulong roleId_;
    /// <summary>
    /// 查询的玩家 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 3;
    private uint serverId_;
    /// <summary>
    /// 查询的服务器 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamQueryReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamQueryReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teamTypes_.Equals(other.teamTypes_)) return false;
      if (RoleId != other.RoleId) return false;
      if (ServerId != other.ServerId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teamTypes_.GetHashCode();
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teamTypes_.WriteTo(output, _repeated_teamTypes_codec);
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teamTypes_.WriteTo(ref output, _repeated_teamTypes_codec);
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teamTypes_.CalculateSize(_repeated_teamTypes_codec);
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamQueryReq other) {
      if (other == null) {
        return;
      }
      teamTypes_.Add(other.teamTypes_);
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            teamTypes_.AddEntriesFrom(input, _repeated_teamTypes_codec);
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10:
          case 8: {
            teamTypes_.AddEntriesFrom(ref input, _repeated_teamTypes_codec);
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamQueryResp : pb::IMessage<TeamQueryResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamQueryResp> _parser = new pb::MessageParser<TeamQueryResp>(() => new TeamQueryResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamQueryResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryResp(TeamQueryResp other) : this() {
      teams_ = other.teams_.Clone();
      roleId_ = other.roleId_;
      serverId_ = other.serverId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryResp Clone() {
      return new TeamQueryResp(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.QueryTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.QueryTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.QueryTeam> teams_ = new pbc::RepeatedField<global::Fight.QueryTeam>();
    /// <summary>
    /// 布阵队伍详细信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.QueryTeam> Teams {
      get { return teams_; }
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 2;
    private ulong roleId_;
    /// <summary>
    /// 查询的玩家 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 3;
    private uint serverId_;
    /// <summary>
    /// 查询的服务器 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamQueryResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamQueryResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      if (RoleId != other.RoleId) return false;
      if (ServerId != other.ServerId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (RoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(RoleId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamQueryResp other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
          case 16: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战斗调试 3303
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleDebugReq : pb::IMessage<BattleDebugReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleDebugReq> _parser = new pb::MessageParser<BattleDebugReq>(() => new BattleDebugReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleDebugReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleDebugReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleDebugReq(BattleDebugReq other) : this() {
      attacker_ = other.attacker_ != null ? other.attacker_.Clone() : null;
      defender_ = other.defender_ != null ? other.defender_.Clone() : null;
      level_ = other.level_;
      starStage_ = other.starStage_;
      skillLevel_ = other.skillLevel_;
      hpTimes_ = other.hpTimes_;
      args_ = other.args_ != null ? other.args_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleDebugReq Clone() {
      return new BattleDebugReq(this);
    }

    /// <summary>Field number for the "attacker" field.</summary>
    public const int AttackerFieldNumber = 1;
    private global::Fight.DebugTeam attacker_;
    /// <summary>
    /// 进攻方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fight.DebugTeam Attacker {
      get { return attacker_; }
      set {
        attacker_ = value;
      }
    }

    /// <summary>Field number for the "defender" field.</summary>
    public const int DefenderFieldNumber = 2;
    private global::Fight.DebugTeam defender_;
    /// <summary>
    /// 防御方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fight.DebugTeam Defender {
      get { return defender_; }
      set {
        defender_ = value;
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 3;
    private int level_;
    /// <summary>
    /// 英雄等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "star_stage" field.</summary>
    public const int StarStageFieldNumber = 4;
    private int starStage_;
    /// <summary>
    /// 英雄星阶
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int StarStage {
      get { return starStage_; }
      set {
        starStage_ = value;
      }
    }

    /// <summary>Field number for the "skill_level" field.</summary>
    public const int SkillLevelFieldNumber = 5;
    private int skillLevel_;
    /// <summary>
    /// 技能等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int SkillLevel {
      get { return skillLevel_; }
      set {
        skillLevel_ = value;
      }
    }

    /// <summary>Field number for the "hp_times" field.</summary>
    public const int HpTimesFieldNumber = 6;
    private int hpTimes_;
    /// <summary>
    /// 初始生命值倍数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int HpTimes {
      get { return hpTimes_; }
      set {
        hpTimes_ = value;
      }
    }

    /// <summary>Field number for the "args" field.</summary>
    public const int ArgsFieldNumber = 7;
    private global::Battle.BattlePureArgs args_;
    /// <summary>
    /// 战斗参数: 透传给战斗模块
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattlePureArgs Args {
      get { return args_; }
      set {
        args_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleDebugReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleDebugReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Attacker, other.Attacker)) return false;
      if (!object.Equals(Defender, other.Defender)) return false;
      if (Level != other.Level) return false;
      if (StarStage != other.StarStage) return false;
      if (SkillLevel != other.SkillLevel) return false;
      if (HpTimes != other.HpTimes) return false;
      if (!object.Equals(Args, other.Args)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (attacker_ != null) hash ^= Attacker.GetHashCode();
      if (defender_ != null) hash ^= Defender.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (StarStage != 0) hash ^= StarStage.GetHashCode();
      if (SkillLevel != 0) hash ^= SkillLevel.GetHashCode();
      if (HpTimes != 0) hash ^= HpTimes.GetHashCode();
      if (args_ != null) hash ^= Args.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (attacker_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Defender);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Level);
      }
      if (StarStage != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(StarStage);
      }
      if (SkillLevel != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(SkillLevel);
      }
      if (HpTimes != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(HpTimes);
      }
      if (args_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Args);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (attacker_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Defender);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Level);
      }
      if (StarStage != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(StarStage);
      }
      if (SkillLevel != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(SkillLevel);
      }
      if (HpTimes != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(HpTimes);
      }
      if (args_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Args);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (attacker_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Attacker);
      }
      if (defender_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Defender);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Level);
      }
      if (StarStage != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(StarStage);
      }
      if (SkillLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SkillLevel);
      }
      if (HpTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HpTimes);
      }
      if (args_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Args);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleDebugReq other) {
      if (other == null) {
        return;
      }
      if (other.attacker_ != null) {
        if (attacker_ == null) {
          Attacker = new global::Fight.DebugTeam();
        }
        Attacker.MergeFrom(other.Attacker);
      }
      if (other.defender_ != null) {
        if (defender_ == null) {
          Defender = new global::Fight.DebugTeam();
        }
        Defender.MergeFrom(other.Defender);
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.StarStage != 0) {
        StarStage = other.StarStage;
      }
      if (other.SkillLevel != 0) {
        SkillLevel = other.SkillLevel;
      }
      if (other.HpTimes != 0) {
        HpTimes = other.HpTimes;
      }
      if (other.args_ != null) {
        if (args_ == null) {
          Args = new global::Battle.BattlePureArgs();
        }
        Args.MergeFrom(other.Args);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (attacker_ == null) {
              Attacker = new global::Fight.DebugTeam();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 18: {
            if (defender_ == null) {
              Defender = new global::Fight.DebugTeam();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 24: {
            Level = input.ReadInt32();
            break;
          }
          case 32: {
            StarStage = input.ReadInt32();
            break;
          }
          case 40: {
            SkillLevel = input.ReadInt32();
            break;
          }
          case 48: {
            HpTimes = input.ReadInt32();
            break;
          }
          case 58: {
            if (args_ == null) {
              Args = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(Args);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (attacker_ == null) {
              Attacker = new global::Fight.DebugTeam();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 18: {
            if (defender_ == null) {
              Defender = new global::Fight.DebugTeam();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 24: {
            Level = input.ReadInt32();
            break;
          }
          case 32: {
            StarStage = input.ReadInt32();
            break;
          }
          case 40: {
            SkillLevel = input.ReadInt32();
            break;
          }
          case 48: {
            HpTimes = input.ReadInt32();
            break;
          }
          case 58: {
            if (args_ == null) {
              Args = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(Args);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleDebugResp : pb::IMessage<BattleDebugResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleDebugResp> _parser = new pb::MessageParser<BattleDebugResp>(() => new BattleDebugResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleDebugResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleDebugResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleDebugResp(BattleDebugResp other) : this() {
      reportId_ = other.reportId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleDebugResp Clone() {
      return new BattleDebugResp(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleDebugResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleDebugResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleDebugResp other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 防守队伍修改 3304
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamDefendModifyReq : pb::IMessage<TeamDefendModifyReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamDefendModifyReq> _parser = new pb::MessageParser<TeamDefendModifyReq>(() => new TeamDefendModifyReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamDefendModifyReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamDefendModifyReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamDefendModifyReq(TeamDefendModifyReq other) : this() {
      teams_ = other.teams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamDefendModifyReq Clone() {
      return new TeamDefendModifyReq(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.DefendTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.DefendTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.DefendTeam> teams_ = new pbc::RepeatedField<global::Fight.DefendTeam>();
    /// <summary>
    /// 所有参与防守的队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.DefendTeam> Teams {
      get { return teams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamDefendModifyReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamDefendModifyReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamDefendModifyReq other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamDefendModifyResp : pb::IMessage<TeamDefendModifyResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamDefendModifyResp> _parser = new pb::MessageParser<TeamDefendModifyResp>(() => new TeamDefendModifyResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamDefendModifyResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamDefendModifyResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamDefendModifyResp(TeamDefendModifyResp other) : this() {
      teams_ = other.teams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamDefendModifyResp Clone() {
      return new TeamDefendModifyResp(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.DefendTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.DefendTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.DefendTeam> teams_ = new pbc::RepeatedField<global::Fight.DefendTeam>();
    /// <summary>
    /// 所有参与防守的队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.DefendTeam> Teams {
      get { return teams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamDefendModifyResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamDefendModifyResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamDefendModifyResp other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 通过阵容创建战斗 3305
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleCreateByFormationReq : pb::IMessage<BattleCreateByFormationReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleCreateByFormationReq> _parser = new pb::MessageParser<BattleCreateByFormationReq>(() => new BattleCreateByFormationReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleCreateByFormationReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByFormationReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByFormationReq(BattleCreateByFormationReq other) : this() {
      battleType_ = other.battleType_;
      attackerRoleId_ = other.attackerRoleId_;
      defenderRoleId_ = other.defenderRoleId_;
      attackerTeamTypes_ = other.attackerTeamTypes_.Clone();
      defenderTeamTypes_ = other.defenderTeamTypes_.Clone();
      logicArgs_ = other.logicArgs_ != null ? other.logicArgs_.Clone() : null;
      pureArgs_ = other.pureArgs_ != null ? other.pureArgs_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByFormationReq Clone() {
      return new BattleCreateByFormationReq(this);
    }

    /// <summary>Field number for the "battle_type" field.</summary>
    public const int BattleTypeFieldNumber = 1;
    private global::PbGameconfig.battle_types battleType_ = global::PbGameconfig.battle_types._0;
    /// <summary>
    /// 战斗类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.battle_types BattleType {
      get { return battleType_; }
      set {
        battleType_ = value;
      }
    }

    /// <summary>Field number for the "attacker_role_id" field.</summary>
    public const int AttackerRoleIdFieldNumber = 2;
    private ulong attackerRoleId_;
    /// <summary>
    /// 进攻方角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong AttackerRoleId {
      get { return attackerRoleId_; }
      set {
        attackerRoleId_ = value;
      }
    }

    /// <summary>Field number for the "defender_role_id" field.</summary>
    public const int DefenderRoleIdFieldNumber = 3;
    private ulong defenderRoleId_;
    /// <summary>
    /// 防御方角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DefenderRoleId {
      get { return defenderRoleId_; }
      set {
        defenderRoleId_ = value;
      }
    }

    /// <summary>Field number for the "attacker_team_types" field.</summary>
    public const int AttackerTeamTypesFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Team.TeamType> _repeated_attackerTeamTypes_codec
        = pb::FieldCodec.ForEnum(34, x => (int) x, x => (global::Team.TeamType) x);
    private readonly pbc::RepeatedField<global::Team.TeamType> attackerTeamTypes_ = new pbc::RepeatedField<global::Team.TeamType>();
    /// <summary>
    /// 进攻方队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Team.TeamType> AttackerTeamTypes {
      get { return attackerTeamTypes_; }
    }

    /// <summary>Field number for the "defender_team_types" field.</summary>
    public const int DefenderTeamTypesFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Team.TeamType> _repeated_defenderTeamTypes_codec
        = pb::FieldCodec.ForEnum(42, x => (int) x, x => (global::Team.TeamType) x);
    private readonly pbc::RepeatedField<global::Team.TeamType> defenderTeamTypes_ = new pbc::RepeatedField<global::Team.TeamType>();
    /// <summary>
    /// 防御方队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Team.TeamType> DefenderTeamTypes {
      get { return defenderTeamTypes_; }
    }

    /// <summary>Field number for the "logic_args" field.</summary>
    public const int LogicArgsFieldNumber = 6;
    private global::Battle.BattleLogicArgs logicArgs_;
    /// <summary>
    /// 战斗参数: 透传给其它业务模块
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattleLogicArgs LogicArgs {
      get { return logicArgs_; }
      set {
        logicArgs_ = value;
      }
    }

    /// <summary>Field number for the "pure_args" field.</summary>
    public const int PureArgsFieldNumber = 7;
    private global::Battle.BattlePureArgs pureArgs_;
    /// <summary>
    /// 战斗参数: 透传给其它业务模块
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattlePureArgs PureArgs {
      get { return pureArgs_; }
      set {
        pureArgs_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleCreateByFormationReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleCreateByFormationReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BattleType != other.BattleType) return false;
      if (AttackerRoleId != other.AttackerRoleId) return false;
      if (DefenderRoleId != other.DefenderRoleId) return false;
      if(!attackerTeamTypes_.Equals(other.attackerTeamTypes_)) return false;
      if(!defenderTeamTypes_.Equals(other.defenderTeamTypes_)) return false;
      if (!object.Equals(LogicArgs, other.LogicArgs)) return false;
      if (!object.Equals(PureArgs, other.PureArgs)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BattleType != global::PbGameconfig.battle_types._0) hash ^= BattleType.GetHashCode();
      if (AttackerRoleId != 0UL) hash ^= AttackerRoleId.GetHashCode();
      if (DefenderRoleId != 0UL) hash ^= DefenderRoleId.GetHashCode();
      hash ^= attackerTeamTypes_.GetHashCode();
      hash ^= defenderTeamTypes_.GetHashCode();
      if (logicArgs_ != null) hash ^= LogicArgs.GetHashCode();
      if (pureArgs_ != null) hash ^= PureArgs.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) BattleType);
      }
      if (AttackerRoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(AttackerRoleId);
      }
      if (DefenderRoleId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(DefenderRoleId);
      }
      attackerTeamTypes_.WriteTo(output, _repeated_attackerTeamTypes_codec);
      defenderTeamTypes_.WriteTo(output, _repeated_defenderTeamTypes_codec);
      if (logicArgs_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(LogicArgs);
      }
      if (pureArgs_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(PureArgs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) BattleType);
      }
      if (AttackerRoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(AttackerRoleId);
      }
      if (DefenderRoleId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(DefenderRoleId);
      }
      attackerTeamTypes_.WriteTo(ref output, _repeated_attackerTeamTypes_codec);
      defenderTeamTypes_.WriteTo(ref output, _repeated_defenderTeamTypes_codec);
      if (logicArgs_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(LogicArgs);
      }
      if (pureArgs_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(PureArgs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BattleType != global::PbGameconfig.battle_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) BattleType);
      }
      if (AttackerRoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(AttackerRoleId);
      }
      if (DefenderRoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(DefenderRoleId);
      }
      size += attackerTeamTypes_.CalculateSize(_repeated_attackerTeamTypes_codec);
      size += defenderTeamTypes_.CalculateSize(_repeated_defenderTeamTypes_codec);
      if (logicArgs_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(LogicArgs);
      }
      if (pureArgs_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(PureArgs);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleCreateByFormationReq other) {
      if (other == null) {
        return;
      }
      if (other.BattleType != global::PbGameconfig.battle_types._0) {
        BattleType = other.BattleType;
      }
      if (other.AttackerRoleId != 0UL) {
        AttackerRoleId = other.AttackerRoleId;
      }
      if (other.DefenderRoleId != 0UL) {
        DefenderRoleId = other.DefenderRoleId;
      }
      attackerTeamTypes_.Add(other.attackerTeamTypes_);
      defenderTeamTypes_.Add(other.defenderTeamTypes_);
      if (other.logicArgs_ != null) {
        if (logicArgs_ == null) {
          LogicArgs = new global::Battle.BattleLogicArgs();
        }
        LogicArgs.MergeFrom(other.LogicArgs);
      }
      if (other.pureArgs_ != null) {
        if (pureArgs_ == null) {
          PureArgs = new global::Battle.BattlePureArgs();
        }
        PureArgs.MergeFrom(other.PureArgs);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 16: {
            AttackerRoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            DefenderRoleId = input.ReadUInt64();
            break;
          }
          case 34:
          case 32: {
            attackerTeamTypes_.AddEntriesFrom(input, _repeated_attackerTeamTypes_codec);
            break;
          }
          case 42:
          case 40: {
            defenderTeamTypes_.AddEntriesFrom(input, _repeated_defenderTeamTypes_codec);
            break;
          }
          case 50: {
            if (logicArgs_ == null) {
              LogicArgs = new global::Battle.BattleLogicArgs();
            }
            input.ReadMessage(LogicArgs);
            break;
          }
          case 58: {
            if (pureArgs_ == null) {
              PureArgs = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(PureArgs);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 16: {
            AttackerRoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            DefenderRoleId = input.ReadUInt64();
            break;
          }
          case 34:
          case 32: {
            attackerTeamTypes_.AddEntriesFrom(ref input, _repeated_attackerTeamTypes_codec);
            break;
          }
          case 42:
          case 40: {
            defenderTeamTypes_.AddEntriesFrom(ref input, _repeated_defenderTeamTypes_codec);
            break;
          }
          case 50: {
            if (logicArgs_ == null) {
              LogicArgs = new global::Battle.BattleLogicArgs();
            }
            input.ReadMessage(LogicArgs);
            break;
          }
          case 58: {
            if (pureArgs_ == null) {
              PureArgs = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(PureArgs);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleCreateByFormationResp : pb::IMessage<BattleCreateByFormationResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleCreateByFormationResp> _parser = new pb::MessageParser<BattleCreateByFormationResp>(() => new BattleCreateByFormationResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleCreateByFormationResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByFormationResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByFormationResp(BattleCreateByFormationResp other) : this() {
      results_ = other.results_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByFormationResp Clone() {
      return new BattleCreateByFormationResp(this);
    }

    /// <summary>Field number for the "results" field.</summary>
    public const int ResultsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.BattleCreateResult> _repeated_results_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.BattleCreateResult.Parser);
    private readonly pbc::RepeatedField<global::Fight.BattleCreateResult> results_ = new pbc::RepeatedField<global::Fight.BattleCreateResult>();
    /// <summary>
    /// 战斗结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.BattleCreateResult> Results {
      get { return results_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleCreateByFormationResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleCreateByFormationResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!results_.Equals(other.results_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= results_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      results_.WriteTo(output, _repeated_results_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      results_.WriteTo(ref output, _repeated_results_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += results_.CalculateSize(_repeated_results_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleCreateByFormationResp other) {
      if (other == null) {
        return;
      }
      results_.Add(other.results_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            results_.AddEntriesFrom(input, _repeated_results_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            results_.AddEntriesFrom(ref input, _repeated_results_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战斗队伍查询 3306
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleTeamQueryReq : pb::IMessage<BattleTeamQueryReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleTeamQueryReq> _parser = new pb::MessageParser<BattleTeamQueryReq>(() => new BattleTeamQueryReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleTeamQueryReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamQueryReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamQueryReq(BattleTeamQueryReq other) : this() {
      teamTypes_ = other.teamTypes_.Clone();
      isAutoFormation_ = other.isAutoFormation_;
      isAllowTeamEmpty_ = other.isAllowTeamEmpty_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamQueryReq Clone() {
      return new BattleTeamQueryReq(this);
    }

    /// <summary>Field number for the "team_types" field.</summary>
    public const int TeamTypesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Team.TeamType> _repeated_teamTypes_codec
        = pb::FieldCodec.ForEnum(10, x => (int) x, x => (global::Team.TeamType) x);
    private readonly pbc::RepeatedField<global::Team.TeamType> teamTypes_ = new pbc::RepeatedField<global::Team.TeamType>();
    /// <summary>
    /// 队伍类型列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Team.TeamType> TeamTypes {
      get { return teamTypes_; }
    }

    /// <summary>Field number for the "is_auto_formation" field.</summary>
    public const int IsAutoFormationFieldNumber = 2;
    private bool isAutoFormation_;
    /// <summary>
    /// 当队伍类型不存在时，是否自动布阵
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsAutoFormation {
      get { return isAutoFormation_; }
      set {
        isAutoFormation_ = value;
      }
    }

    /// <summary>Field number for the "is_allow_team_empty" field.</summary>
    public const int IsAllowTeamEmptyFieldNumber = 3;
    private bool isAllowTeamEmpty_;
    /// <summary>
    /// 是否允许空队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsAllowTeamEmpty {
      get { return isAllowTeamEmpty_; }
      set {
        isAllowTeamEmpty_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleTeamQueryReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleTeamQueryReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teamTypes_.Equals(other.teamTypes_)) return false;
      if (IsAutoFormation != other.IsAutoFormation) return false;
      if (IsAllowTeamEmpty != other.IsAllowTeamEmpty) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teamTypes_.GetHashCode();
      if (IsAutoFormation != false) hash ^= IsAutoFormation.GetHashCode();
      if (IsAllowTeamEmpty != false) hash ^= IsAllowTeamEmpty.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teamTypes_.WriteTo(output, _repeated_teamTypes_codec);
      if (IsAutoFormation != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsAutoFormation);
      }
      if (IsAllowTeamEmpty != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsAllowTeamEmpty);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teamTypes_.WriteTo(ref output, _repeated_teamTypes_codec);
      if (IsAutoFormation != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsAutoFormation);
      }
      if (IsAllowTeamEmpty != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsAllowTeamEmpty);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teamTypes_.CalculateSize(_repeated_teamTypes_codec);
      if (IsAutoFormation != false) {
        size += 1 + 1;
      }
      if (IsAllowTeamEmpty != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleTeamQueryReq other) {
      if (other == null) {
        return;
      }
      teamTypes_.Add(other.teamTypes_);
      if (other.IsAutoFormation != false) {
        IsAutoFormation = other.IsAutoFormation;
      }
      if (other.IsAllowTeamEmpty != false) {
        IsAllowTeamEmpty = other.IsAllowTeamEmpty;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            teamTypes_.AddEntriesFrom(input, _repeated_teamTypes_codec);
            break;
          }
          case 16: {
            IsAutoFormation = input.ReadBool();
            break;
          }
          case 24: {
            IsAllowTeamEmpty = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10:
          case 8: {
            teamTypes_.AddEntriesFrom(ref input, _repeated_teamTypes_codec);
            break;
          }
          case 16: {
            IsAutoFormation = input.ReadBool();
            break;
          }
          case 24: {
            IsAllowTeamEmpty = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleTeamQueryResp : pb::IMessage<BattleTeamQueryResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleTeamQueryResp> _parser = new pb::MessageParser<BattleTeamQueryResp>(() => new BattleTeamQueryResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleTeamQueryResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamQueryResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamQueryResp(BattleTeamQueryResp other) : this() {
      teams_ = other.teams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamQueryResp Clone() {
      return new BattleTeamQueryResp(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Battle.Team> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Battle.Team.Parser);
    private readonly pbc::RepeatedField<global::Battle.Team> teams_ = new pbc::RepeatedField<global::Battle.Team>();
    /// <summary>
    /// 队伍信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Battle.Team> Teams {
      get { return teams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleTeamQueryResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleTeamQueryResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleTeamQueryResp other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 队伍状态变更 3307
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushTeamStatus : pb::IMessage<PushTeamStatus>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushTeamStatus> _parser = new pb::MessageParser<PushTeamStatus>(() => new PushTeamStatus());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushTeamStatus> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushTeamStatus() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushTeamStatus(PushTeamStatus other) : this() {
      teamType_ = other.teamType_;
      status_ = other.status_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushTeamStatus Clone() {
      return new PushTeamStatus(this);
    }

    /// <summary>Field number for the "team_type" field.</summary>
    public const int TeamTypeFieldNumber = 1;
    private global::Team.TeamType teamType_ = global::Team.TeamType.TeamNil;
    /// <summary>
    /// 队伍类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Team.TeamType TeamType {
      get { return teamType_; }
      set {
        teamType_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 2;
    private global::Fight.TeamStatus status_ = global::Fight.TeamStatus.Garrison;
    /// <summary>
    /// 队伍状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fight.TeamStatus Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushTeamStatus);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushTeamStatus other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TeamType != other.TeamType) return false;
      if (Status != other.Status) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (TeamType != global::Team.TeamType.TeamNil) hash ^= TeamType.GetHashCode();
      if (Status != global::Fight.TeamStatus.Garrison) hash ^= Status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      if (Status != global::Fight.TeamStatus.Garrison) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      if (Status != global::Fight.TeamStatus.Garrison) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (TeamType != global::Team.TeamType.TeamNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TeamType);
      }
      if (Status != global::Fight.TeamStatus.Garrison) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushTeamStatus other) {
      if (other == null) {
        return;
      }
      if (other.TeamType != global::Team.TeamType.TeamNil) {
        TeamType = other.TeamType;
      }
      if (other.Status != global::Fight.TeamStatus.Garrison) {
        Status = other.Status;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 16: {
            Status = (global::Fight.TeamStatus) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 16: {
            Status = (global::Fight.TeamStatus) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 推送布阵队伍变更 3308
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushFormationTeam : pb::IMessage<PushFormationTeam>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushFormationTeam> _parser = new pb::MessageParser<PushFormationTeam>(() => new PushFormationTeam());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushFormationTeam> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushFormationTeam() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushFormationTeam(PushFormationTeam other) : this() {
      teams_ = other.teams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushFormationTeam Clone() {
      return new PushFormationTeam(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.FormationTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.FormationTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.FormationTeam> teams_ = new pbc::RepeatedField<global::Fight.FormationTeam>();
    /// <summary>
    /// 布阵队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.FormationTeam> Teams {
      get { return teams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushFormationTeam);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushFormationTeam other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushFormationTeam other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 推送防守队伍变更 3309
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushDefendTeam : pb::IMessage<PushDefendTeam>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushDefendTeam> _parser = new pb::MessageParser<PushDefendTeam>(() => new PushDefendTeam());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushDefendTeam> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushDefendTeam() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushDefendTeam(PushDefendTeam other) : this() {
      teams_ = other.teams_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushDefendTeam Clone() {
      return new PushDefendTeam(this);
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.DefendTeam> _repeated_teams_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.DefendTeam.Parser);
    private readonly pbc::RepeatedField<global::Fight.DefendTeam> teams_ = new pbc::RepeatedField<global::Fight.DefendTeam>();
    /// <summary>
    /// 防守队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.DefendTeam> Teams {
      get { return teams_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushDefendTeam);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushDefendTeam other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!teams_.Equals(other.teams_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= teams_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      teams_.WriteTo(output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushDefendTeam other) {
      if (other == null) {
        return;
      }
      teams_.Add(other.teams_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 通过怪物创建战斗 3310
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleCreateByMonsterReq : pb::IMessage<BattleCreateByMonsterReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleCreateByMonsterReq> _parser = new pb::MessageParser<BattleCreateByMonsterReq>(() => new BattleCreateByMonsterReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleCreateByMonsterReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByMonsterReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByMonsterReq(BattleCreateByMonsterReq other) : this() {
      battleType_ = other.battleType_;
      attackerRoleId_ = other.attackerRoleId_;
      attackerTeamType_ = other.attackerTeamType_;
      defenderMonsterId_ = other.defenderMonsterId_;
      logicArgs_ = other.logicArgs_ != null ? other.logicArgs_.Clone() : null;
      pureArgs_ = other.pureArgs_ != null ? other.pureArgs_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByMonsterReq Clone() {
      return new BattleCreateByMonsterReq(this);
    }

    /// <summary>Field number for the "battle_type" field.</summary>
    public const int BattleTypeFieldNumber = 1;
    private global::PbGameconfig.battle_types battleType_ = global::PbGameconfig.battle_types._0;
    /// <summary>
    /// 战斗类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.battle_types BattleType {
      get { return battleType_; }
      set {
        battleType_ = value;
      }
    }

    /// <summary>Field number for the "attacker_role_id" field.</summary>
    public const int AttackerRoleIdFieldNumber = 2;
    private ulong attackerRoleId_;
    /// <summary>
    /// 进攻方角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong AttackerRoleId {
      get { return attackerRoleId_; }
      set {
        attackerRoleId_ = value;
      }
    }

    /// <summary>Field number for the "attacker_team_type" field.</summary>
    public const int AttackerTeamTypeFieldNumber = 3;
    private global::Team.TeamType attackerTeamType_ = global::Team.TeamType.TeamNil;
    /// <summary>
    /// 进攻方队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Team.TeamType AttackerTeamType {
      get { return attackerTeamType_; }
      set {
        attackerTeamType_ = value;
      }
    }

    /// <summary>Field number for the "defender_monster_id" field.</summary>
    public const int DefenderMonsterIdFieldNumber = 4;
    private int defenderMonsterId_;
    /// <summary>
    /// 防御方怪物组 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DefenderMonsterId {
      get { return defenderMonsterId_; }
      set {
        defenderMonsterId_ = value;
      }
    }

    /// <summary>Field number for the "logic_args" field.</summary>
    public const int LogicArgsFieldNumber = 5;
    private global::Battle.BattleLogicArgs logicArgs_;
    /// <summary>
    /// 战斗参数: 透传给其它业务模块
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattleLogicArgs LogicArgs {
      get { return logicArgs_; }
      set {
        logicArgs_ = value;
      }
    }

    /// <summary>Field number for the "pure_args" field.</summary>
    public const int PureArgsFieldNumber = 6;
    private global::Battle.BattlePureArgs pureArgs_;
    /// <summary>
    /// 战斗参数: 透传给其它业务模块
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattlePureArgs PureArgs {
      get { return pureArgs_; }
      set {
        pureArgs_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleCreateByMonsterReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleCreateByMonsterReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BattleType != other.BattleType) return false;
      if (AttackerRoleId != other.AttackerRoleId) return false;
      if (AttackerTeamType != other.AttackerTeamType) return false;
      if (DefenderMonsterId != other.DefenderMonsterId) return false;
      if (!object.Equals(LogicArgs, other.LogicArgs)) return false;
      if (!object.Equals(PureArgs, other.PureArgs)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BattleType != global::PbGameconfig.battle_types._0) hash ^= BattleType.GetHashCode();
      if (AttackerRoleId != 0UL) hash ^= AttackerRoleId.GetHashCode();
      if (AttackerTeamType != global::Team.TeamType.TeamNil) hash ^= AttackerTeamType.GetHashCode();
      if (DefenderMonsterId != 0) hash ^= DefenderMonsterId.GetHashCode();
      if (logicArgs_ != null) hash ^= LogicArgs.GetHashCode();
      if (pureArgs_ != null) hash ^= PureArgs.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) BattleType);
      }
      if (AttackerRoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(AttackerRoleId);
      }
      if (AttackerTeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) AttackerTeamType);
      }
      if (DefenderMonsterId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(DefenderMonsterId);
      }
      if (logicArgs_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(LogicArgs);
      }
      if (pureArgs_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(PureArgs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) BattleType);
      }
      if (AttackerRoleId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(AttackerRoleId);
      }
      if (AttackerTeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) AttackerTeamType);
      }
      if (DefenderMonsterId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(DefenderMonsterId);
      }
      if (logicArgs_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(LogicArgs);
      }
      if (pureArgs_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(PureArgs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BattleType != global::PbGameconfig.battle_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) BattleType);
      }
      if (AttackerRoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(AttackerRoleId);
      }
      if (AttackerTeamType != global::Team.TeamType.TeamNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) AttackerTeamType);
      }
      if (DefenderMonsterId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DefenderMonsterId);
      }
      if (logicArgs_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(LogicArgs);
      }
      if (pureArgs_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(PureArgs);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleCreateByMonsterReq other) {
      if (other == null) {
        return;
      }
      if (other.BattleType != global::PbGameconfig.battle_types._0) {
        BattleType = other.BattleType;
      }
      if (other.AttackerRoleId != 0UL) {
        AttackerRoleId = other.AttackerRoleId;
      }
      if (other.AttackerTeamType != global::Team.TeamType.TeamNil) {
        AttackerTeamType = other.AttackerTeamType;
      }
      if (other.DefenderMonsterId != 0) {
        DefenderMonsterId = other.DefenderMonsterId;
      }
      if (other.logicArgs_ != null) {
        if (logicArgs_ == null) {
          LogicArgs = new global::Battle.BattleLogicArgs();
        }
        LogicArgs.MergeFrom(other.LogicArgs);
      }
      if (other.pureArgs_ != null) {
        if (pureArgs_ == null) {
          PureArgs = new global::Battle.BattlePureArgs();
        }
        PureArgs.MergeFrom(other.PureArgs);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 16: {
            AttackerRoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            AttackerTeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 32: {
            DefenderMonsterId = input.ReadInt32();
            break;
          }
          case 42: {
            if (logicArgs_ == null) {
              LogicArgs = new global::Battle.BattleLogicArgs();
            }
            input.ReadMessage(LogicArgs);
            break;
          }
          case 50: {
            if (pureArgs_ == null) {
              PureArgs = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(PureArgs);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 16: {
            AttackerRoleId = input.ReadUInt64();
            break;
          }
          case 24: {
            AttackerTeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 32: {
            DefenderMonsterId = input.ReadInt32();
            break;
          }
          case 42: {
            if (logicArgs_ == null) {
              LogicArgs = new global::Battle.BattleLogicArgs();
            }
            input.ReadMessage(LogicArgs);
            break;
          }
          case 50: {
            if (pureArgs_ == null) {
              PureArgs = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(PureArgs);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleCreateByMonsterResp : pb::IMessage<BattleCreateByMonsterResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleCreateByMonsterResp> _parser = new pb::MessageParser<BattleCreateByMonsterResp>(() => new BattleCreateByMonsterResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleCreateByMonsterResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByMonsterResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByMonsterResp(BattleCreateByMonsterResp other) : this() {
      result_ = other.result_ != null ? other.result_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateByMonsterResp Clone() {
      return new BattleCreateByMonsterResp(this);
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 1;
    private global::Fight.BattleCreateResult result_;
    /// <summary>
    /// 战斗结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fight.BattleCreateResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleCreateByMonsterResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleCreateByMonsterResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Result, other.Result)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (result_ != null) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleCreateByMonsterResp other) {
      if (other == null) {
        return;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::Fight.BattleCreateResult();
        }
        Result.MergeFrom(other.Result);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Fight.BattleCreateResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Fight.BattleCreateResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 查看小队战力组成 3311
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamQueryPowerBuildReq : pb::IMessage<TeamQueryPowerBuildReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamQueryPowerBuildReq> _parser = new pb::MessageParser<TeamQueryPowerBuildReq>(() => new TeamQueryPowerBuildReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamQueryPowerBuildReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryPowerBuildReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryPowerBuildReq(TeamQueryPowerBuildReq other) : this() {
      teamType_ = other.teamType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryPowerBuildReq Clone() {
      return new TeamQueryPowerBuildReq(this);
    }

    /// <summary>Field number for the "team_type" field.</summary>
    public const int TeamTypeFieldNumber = 1;
    private global::Team.TeamType teamType_ = global::Team.TeamType.TeamNil;
    /// <summary>
    /// 队伍类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Team.TeamType TeamType {
      get { return teamType_; }
      set {
        teamType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamQueryPowerBuildReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamQueryPowerBuildReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TeamType != other.TeamType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (TeamType != global::Team.TeamType.TeamNil) hash ^= TeamType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (TeamType != global::Team.TeamType.TeamNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TeamType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamQueryPowerBuildReq other) {
      if (other == null) {
        return;
      }
      if (other.TeamType != global::Team.TeamType.TeamNil) {
        TeamType = other.TeamType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamQueryPowerBuildResp : pb::IMessage<TeamQueryPowerBuildResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamQueryPowerBuildResp> _parser = new pb::MessageParser<TeamQueryPowerBuildResp>(() => new TeamQueryPowerBuildResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamQueryPowerBuildResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryPowerBuildResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryPowerBuildResp(TeamQueryPowerBuildResp other) : this() {
      builds_ = other.builds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamQueryPowerBuildResp Clone() {
      return new TeamQueryPowerBuildResp(this);
    }

    /// <summary>Field number for the "builds" field.</summary>
    public const int BuildsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Common.TeamPowerBuild> _repeated_builds_codec
        = pb::FieldCodec.ForMessage(10, global::Common.TeamPowerBuild.Parser);
    private readonly pbc::RepeatedField<global::Common.TeamPowerBuild> builds_ = new pbc::RepeatedField<global::Common.TeamPowerBuild>();
    /// <summary>
    /// 战力组成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.TeamPowerBuild> Builds {
      get { return builds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamQueryPowerBuildResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamQueryPowerBuildResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!builds_.Equals(other.builds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= builds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      builds_.WriteTo(output, _repeated_builds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      builds_.WriteTo(ref output, _repeated_builds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += builds_.CalculateSize(_repeated_builds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamQueryPowerBuildResp other) {
      if (other == null) {
        return;
      }
      builds_.Add(other.builds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            builds_.AddEntriesFrom(input, _repeated_builds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            builds_.AddEntriesFrom(ref input, _repeated_builds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战斗调试队伍
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class DebugTeam : pb::IMessage<DebugTeam>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DebugTeam> _parser = new pb::MessageParser<DebugTeam>(() => new DebugTeam());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DebugTeam> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DebugTeam() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DebugTeam(DebugTeam other) : this() {
      heroes_ = other.heroes_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DebugTeam Clone() {
      return new DebugTeam(this);
    }

    /// <summary>Field number for the "heroes" field.</summary>
    public const int HeroesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Fight.DebugHero> _repeated_heroes_codec
        = pb::FieldCodec.ForMessage(10, global::Fight.DebugHero.Parser);
    private readonly pbc::RepeatedField<global::Fight.DebugHero> heroes_ = new pbc::RepeatedField<global::Fight.DebugHero>();
    /// <summary>
    /// 英雄信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.DebugHero> Heroes {
      get { return heroes_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DebugTeam);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DebugTeam other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!heroes_.Equals(other.heroes_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= heroes_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      heroes_.WriteTo(output, _repeated_heroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      heroes_.WriteTo(ref output, _repeated_heroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += heroes_.CalculateSize(_repeated_heroes_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DebugTeam other) {
      if (other == null) {
        return;
      }
      heroes_.Add(other.heroes_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            heroes_.AddEntriesFrom(input, _repeated_heroes_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            heroes_.AddEntriesFrom(ref input, _repeated_heroes_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class DebugHero : pb::IMessage<DebugHero>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DebugHero> _parser = new pb::MessageParser<DebugHero>(() => new DebugHero());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DebugHero> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DebugHero() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DebugHero(DebugHero other) : this() {
      code_ = other.code_;
      pos_ = other.pos_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DebugHero Clone() {
      return new DebugHero(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int CodeFieldNumber = 1;
    private global::PbGameconfig.itemid code_ = global::PbGameconfig.itemid.Nil;
    /// <summary>
    /// 英雄 id, hero_config.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.itemid Code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "pos" field.</summary>
    public const int PosFieldNumber = 2;
    private int pos_;
    /// <summary>
    /// 位置, 进攻方 1-5, 防御方 11-15
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Pos {
      get { return pos_; }
      set {
        pos_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DebugHero);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DebugHero other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Code != other.Code) return false;
      if (Pos != other.Pos) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Code != global::PbGameconfig.itemid.Nil) hash ^= Code.GetHashCode();
      if (Pos != 0) hash ^= Pos.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Code != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Code);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Code != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Code);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Code != global::PbGameconfig.itemid.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Code);
      }
      if (Pos != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Pos);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DebugHero other) {
      if (other == null) {
        return;
      }
      if (other.Code != global::PbGameconfig.itemid.Nil) {
        Code = other.Code;
      }
      if (other.Pos != 0) {
        Pos = other.Pos;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Code = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Code = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 布阵队伍
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FormationTeam : pb::IMessage<FormationTeam>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FormationTeam> _parser = new pb::MessageParser<FormationTeam>(() => new FormationTeam());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FormationTeam> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FormationTeam() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FormationTeam(FormationTeam other) : this() {
      teamType_ = other.teamType_;
      heroes_ = other.heroes_.Clone();
      status_ = other.status_;
      power_ = other.power_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FormationTeam Clone() {
      return new FormationTeam(this);
    }

    /// <summary>Field number for the "team_type" field.</summary>
    public const int TeamTypeFieldNumber = 1;
    private global::Team.TeamType teamType_ = global::Team.TeamType.TeamNil;
    /// <summary>
    /// 队伍类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Team.TeamType TeamType {
      get { return teamType_; }
      set {
        teamType_ = value;
      }
    }

    /// <summary>Field number for the "heroes" field.</summary>
    public const int HeroesFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Fight.FormationHero> _repeated_heroes_codec
        = pb::FieldCodec.ForMessage(18, global::Fight.FormationHero.Parser);
    private readonly pbc::RepeatedField<global::Fight.FormationHero> heroes_ = new pbc::RepeatedField<global::Fight.FormationHero>();
    /// <summary>
    /// 布阵英雄
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.FormationHero> Heroes {
      get { return heroes_; }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 3;
    private global::Fight.TeamStatus status_ = global::Fight.TeamStatus.Garrison;
    /// <summary>
    /// 队伍状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fight.TeamStatus Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "power" field.</summary>
    public const int PowerFieldNumber = 4;
    private long power_;
    /// <summary>
    /// 队伍战力, 通过协议 3311 查看队伍战力组成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Power {
      get { return power_; }
      set {
        power_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FormationTeam);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FormationTeam other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TeamType != other.TeamType) return false;
      if(!heroes_.Equals(other.heroes_)) return false;
      if (Status != other.Status) return false;
      if (Power != other.Power) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (TeamType != global::Team.TeamType.TeamNil) hash ^= TeamType.GetHashCode();
      hash ^= heroes_.GetHashCode();
      if (Status != global::Fight.TeamStatus.Garrison) hash ^= Status.GetHashCode();
      if (Power != 0L) hash ^= Power.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      heroes_.WriteTo(output, _repeated_heroes_codec);
      if (Status != global::Fight.TeamStatus.Garrison) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Status);
      }
      if (Power != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Power);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      heroes_.WriteTo(ref output, _repeated_heroes_codec);
      if (Status != global::Fight.TeamStatus.Garrison) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Status);
      }
      if (Power != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Power);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (TeamType != global::Team.TeamType.TeamNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TeamType);
      }
      size += heroes_.CalculateSize(_repeated_heroes_codec);
      if (Status != global::Fight.TeamStatus.Garrison) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Status);
      }
      if (Power != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Power);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FormationTeam other) {
      if (other == null) {
        return;
      }
      if (other.TeamType != global::Team.TeamType.TeamNil) {
        TeamType = other.TeamType;
      }
      heroes_.Add(other.heroes_);
      if (other.Status != global::Fight.TeamStatus.Garrison) {
        Status = other.Status;
      }
      if (other.Power != 0L) {
        Power = other.Power;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 18: {
            heroes_.AddEntriesFrom(input, _repeated_heroes_codec);
            break;
          }
          case 24: {
            Status = (global::Fight.TeamStatus) input.ReadEnum();
            break;
          }
          case 32: {
            Power = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 18: {
            heroes_.AddEntriesFrom(ref input, _repeated_heroes_codec);
            break;
          }
          case 24: {
            Status = (global::Fight.TeamStatus) input.ReadEnum();
            break;
          }
          case 32: {
            Power = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 布阵英雄
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FormationHero : pb::IMessage<FormationHero>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FormationHero> _parser = new pb::MessageParser<FormationHero>(() => new FormationHero());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FormationHero> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FormationHero() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FormationHero(FormationHero other) : this() {
      heroId_ = other.heroId_;
      pos_ = other.pos_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FormationHero Clone() {
      return new FormationHero(this);
    }

    /// <summary>Field number for the "hero_id" field.</summary>
    public const int HeroIdFieldNumber = 1;
    private global::PbGameconfig.itemid heroId_ = global::PbGameconfig.itemid.Nil;
    /// <summary>
    /// hero.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.itemid HeroId {
      get { return heroId_; }
      set {
        heroId_ = value;
      }
    }

    /// <summary>Field number for the "pos" field.</summary>
    public const int PosFieldNumber = 2;
    private int pos_;
    /// <summary>
    /// 位置: 1-5
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Pos {
      get { return pos_; }
      set {
        pos_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FormationHero);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FormationHero other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (HeroId != other.HeroId) return false;
      if (Pos != other.Pos) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HeroId != global::PbGameconfig.itemid.Nil) hash ^= HeroId.GetHashCode();
      if (Pos != 0) hash ^= Pos.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) HeroId);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) HeroId);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) HeroId);
      }
      if (Pos != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Pos);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FormationHero other) {
      if (other == null) {
        return;
      }
      if (other.HeroId != global::PbGameconfig.itemid.Nil) {
        HeroId = other.HeroId;
      }
      if (other.Pos != 0) {
        Pos = other.Pos;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            HeroId = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            HeroId = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 查询布阵队伍信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QueryTeam : pb::IMessage<QueryTeam>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QueryTeam> _parser = new pb::MessageParser<QueryTeam>(() => new QueryTeam());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QueryTeam> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueryTeam() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueryTeam(QueryTeam other) : this() {
      teamType_ = other.teamType_;
      heroes_ = other.heroes_.Clone();
      power_ = other.power_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueryTeam Clone() {
      return new QueryTeam(this);
    }

    /// <summary>Field number for the "team_type" field.</summary>
    public const int TeamTypeFieldNumber = 1;
    private global::Team.TeamType teamType_ = global::Team.TeamType.TeamNil;
    /// <summary>
    /// 队伍类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Team.TeamType TeamType {
      get { return teamType_; }
      set {
        teamType_ = value;
      }
    }

    /// <summary>Field number for the "heroes" field.</summary>
    public const int HeroesFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Fight.QueryTeamHero> _repeated_heroes_codec
        = pb::FieldCodec.ForMessage(18, global::Fight.QueryTeamHero.Parser);
    private readonly pbc::RepeatedField<global::Fight.QueryTeamHero> heroes_ = new pbc::RepeatedField<global::Fight.QueryTeamHero>();
    /// <summary>
    /// 布阵英雄
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Fight.QueryTeamHero> Heroes {
      get { return heroes_; }
    }

    /// <summary>Field number for the "power" field.</summary>
    public const int PowerFieldNumber = 3;
    private long power_;
    /// <summary>
    /// 总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Power {
      get { return power_; }
      set {
        power_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QueryTeam);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QueryTeam other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TeamType != other.TeamType) return false;
      if(!heroes_.Equals(other.heroes_)) return false;
      if (Power != other.Power) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (TeamType != global::Team.TeamType.TeamNil) hash ^= TeamType.GetHashCode();
      hash ^= heroes_.GetHashCode();
      if (Power != 0L) hash ^= Power.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      heroes_.WriteTo(output, _repeated_heroes_codec);
      if (Power != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Power);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      heroes_.WriteTo(ref output, _repeated_heroes_codec);
      if (Power != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Power);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (TeamType != global::Team.TeamType.TeamNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TeamType);
      }
      size += heroes_.CalculateSize(_repeated_heroes_codec);
      if (Power != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Power);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QueryTeam other) {
      if (other == null) {
        return;
      }
      if (other.TeamType != global::Team.TeamType.TeamNil) {
        TeamType = other.TeamType;
      }
      heroes_.Add(other.heroes_);
      if (other.Power != 0L) {
        Power = other.Power;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 18: {
            heroes_.AddEntriesFrom(input, _repeated_heroes_codec);
            break;
          }
          case 24: {
            Power = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 18: {
            heroes_.AddEntriesFrom(ref input, _repeated_heroes_codec);
            break;
          }
          case 24: {
            Power = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 查询布阵队伍时的英雄信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QueryTeamHero : pb::IMessage<QueryTeamHero>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QueryTeamHero> _parser = new pb::MessageParser<QueryTeamHero>(() => new QueryTeamHero());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QueryTeamHero> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueryTeamHero() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueryTeamHero(QueryTeamHero other) : this() {
      heroId_ = other.heroId_;
      pos_ = other.pos_;
      level_ = other.level_;
      starStage_ = other.starStage_;
      power_ = other.power_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueryTeamHero Clone() {
      return new QueryTeamHero(this);
    }

    /// <summary>Field number for the "hero_id" field.</summary>
    public const int HeroIdFieldNumber = 1;
    private global::PbGameconfig.itemid heroId_ = global::PbGameconfig.itemid.Nil;
    /// <summary>
    /// hero.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.itemid HeroId {
      get { return heroId_; }
      set {
        heroId_ = value;
      }
    }

    /// <summary>Field number for the "pos" field.</summary>
    public const int PosFieldNumber = 2;
    private int pos_;
    /// <summary>
    /// 位置: 1-5
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Pos {
      get { return pos_; }
      set {
        pos_ = value;
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 3;
    private uint level_;
    /// <summary>
    /// 等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "star_stage" field.</summary>
    public const int StarStageFieldNumber = 4;
    private uint starStage_;
    /// <summary>
    /// 星阶
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint StarStage {
      get { return starStage_; }
      set {
        starStage_ = value;
      }
    }

    /// <summary>Field number for the "power" field.</summary>
    public const int PowerFieldNumber = 5;
    private ulong power_;
    /// <summary>
    /// 战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Power {
      get { return power_; }
      set {
        power_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QueryTeamHero);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QueryTeamHero other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (HeroId != other.HeroId) return false;
      if (Pos != other.Pos) return false;
      if (Level != other.Level) return false;
      if (StarStage != other.StarStage) return false;
      if (Power != other.Power) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HeroId != global::PbGameconfig.itemid.Nil) hash ^= HeroId.GetHashCode();
      if (Pos != 0) hash ^= Pos.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (StarStage != 0) hash ^= StarStage.GetHashCode();
      if (Power != 0UL) hash ^= Power.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) HeroId);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Level);
      }
      if (StarStage != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(StarStage);
      }
      if (Power != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(Power);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) HeroId);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Level);
      }
      if (StarStage != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(StarStage);
      }
      if (Power != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(Power);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) HeroId);
      }
      if (Pos != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Pos);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Level);
      }
      if (StarStage != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(StarStage);
      }
      if (Power != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Power);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QueryTeamHero other) {
      if (other == null) {
        return;
      }
      if (other.HeroId != global::PbGameconfig.itemid.Nil) {
        HeroId = other.HeroId;
      }
      if (other.Pos != 0) {
        Pos = other.Pos;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.StarStage != 0) {
        StarStage = other.StarStage;
      }
      if (other.Power != 0UL) {
        Power = other.Power;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            HeroId = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
          case 24: {
            Level = input.ReadUInt32();
            break;
          }
          case 32: {
            StarStage = input.ReadUInt32();
            break;
          }
          case 40: {
            Power = input.ReadUInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            HeroId = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
          case 24: {
            Level = input.ReadUInt32();
            break;
          }
          case 32: {
            StarStage = input.ReadUInt32();
            break;
          }
          case 40: {
            Power = input.ReadUInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 防守队伍配置
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class DefendTeam : pb::IMessage<DefendTeam>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DefendTeam> _parser = new pb::MessageParser<DefendTeam>(() => new DefendTeam());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DefendTeam> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DefendTeam() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DefendTeam(DefendTeam other) : this() {
      index_ = other.index_;
      teamType_ = other.teamType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DefendTeam Clone() {
      return new DefendTeam(this);
    }

    /// <summary>Field number for the "index" field.</summary>
    public const int IndexFieldNumber = 1;
    private int index_;
    /// <summary>
    /// 索引 1 ~ 4
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Index {
      get { return index_; }
      set {
        index_ = value;
      }
    }

    /// <summary>Field number for the "team_type" field.</summary>
    public const int TeamTypeFieldNumber = 2;
    private global::Team.TeamType teamType_ = global::Team.TeamType.TeamNil;
    /// <summary>
    /// 队伍类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Team.TeamType TeamType {
      get { return teamType_; }
      set {
        teamType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DefendTeam);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DefendTeam other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Index != other.Index) return false;
      if (TeamType != other.TeamType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Index != 0) hash ^= Index.GetHashCode();
      if (TeamType != global::Team.TeamType.TeamNil) hash ^= TeamType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Index);
      }
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) TeamType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Index);
      }
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) TeamType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Index);
      }
      if (TeamType != global::Team.TeamType.TeamNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TeamType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DefendTeam other) {
      if (other == null) {
        return;
      }
      if (other.Index != 0) {
        Index = other.Index;
      }
      if (other.TeamType != global::Team.TeamType.TeamNil) {
        TeamType = other.TeamType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Index = input.ReadInt32();
            break;
          }
          case 16: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Index = input.ReadInt32();
            break;
          }
          case 16: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 创建战斗回传的结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleCreateResult : pb::IMessage<BattleCreateResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleCreateResult> _parser = new pb::MessageParser<BattleCreateResult>(() => new BattleCreateResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleCreateResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fight.FightReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateResult(BattleCreateResult other) : this() {
      reportId_ = other.reportId_;
      result_ = other.result_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateResult Clone() {
      return new BattleCreateResult(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id/战报组 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 2;
    private int result_;
    /// <summary>
    /// 战斗结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleCreateResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleCreateResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      if (Result != other.Result) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (Result != 0) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (Result != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (Result != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (Result != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleCreateResult other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      if (other.Result != 0) {
        Result = other.Result;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            Result = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            Result = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
