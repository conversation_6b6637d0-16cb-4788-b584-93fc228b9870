// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: tower.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Tower {

  /// <summary>Holder for reflection information generated from tower.proto</summary>
  public static partial class TowerReflection {

    #region Descriptor
    /// <summary>File descriptor for tower.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static TowerReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cgt0b3dlci5wcm90bxIFdG93ZXIaDWFydGljbGUucHJvdG8aEGdhbWVjb25m",
            "aWcucHJvdG8iDgoMVG93ZXJMb2FkUmVxIlcKDVRvd2VyTG9hZFJlc3ASHAoG",
            "dG93ZXJzGAEgAygLMgwudG93ZXIuVG93ZXISDgoGZW5kX2F0GAIgASgDEhgK",
            "EHNlcnZlcl9vcGVuX2RheXMYAyABKAUiRAoOVG93ZXJDaG9vc2VSZXESHgoE",
            "dHlwZRgBIAEoDjIQLnRvd2VyLlRvd2VyVHlwZRISCgpjaGFwdGVyX2lkGAIg",
            "ASgFIjIKD1Rvd2VyQ2hvb3NlUmVzcBIfCgdjaGFwdGVyGAEgASgLMg4udG93",
            "ZXIuQ2hhcHRlciIjCg1Ub3dlckZpZ2h0UmVxEhIKCm1pc3Npb25faWQYASAB",
            "KAUiiwEKDlRvd2VyRmlnaHRSZXNwEh8KB21pc3Npb24YASABKAsyDi50b3dl",
            "ci5NaXNzaW9uEiEKB3Jld2FyZHMYAiADKAsyEC5hcnRpY2xlLkFydGljbGUS",
            "EQoJcmVwb3J0X2lkGAMgASgJEiIKBmhlcm9lcxgEIAMoCzISLnRvd2VyLlVw",
            "Z3JhZGVIZXJvIrIBCgVUb3dlchIeCgR0eXBlGAEgASgOMhAudG93ZXIuVG93",
            "ZXJUeXBlEhYKDm1heF9jaGFwdGVyX2lkGAIgASgFEiQKDGN1cnJfY2hhcHRl",
            "chgDIAEoCzIOLnRvd2VyLkNoYXB0ZXISEwoLZW50cnlfdGltZXMYBCABKAUS",
            "HgoWY2hhbGxlbmdlZF9jaGFwdGVyX2lkcxgFIAMoBRIWCg5lbnRlcmVkX2hl",
            "cm9lcxgGIAMoBSI/CgdDaGFwdGVyEhIKCmNoYXB0ZXJfaWQYASABKAUSIAoI",
            "bWlzc2lvbnMYAiADKAsyDi50b3dlci5NaXNzaW9uIi0KB01pc3Npb24SEgoK",
            "bWlzc2lvbl9pZBgBIAEoBRIOCgZpc193aW4YAiABKAgiSQoLVXBncmFkZUhl",
            "cm8SJgoHaGVyb19pZBgBIAEoDjIVLnBiX2dhbWVjb25maWcuaXRlbWlkEhIK",
            "Cmhlcm9fbGV2ZWwYAiABKAUqOQoJVG93ZXJUeXBlEgcKA05pbBAAEggKBFRh",
            "bmsQARIMCghBaXJwbGFuZRACEgsKB01pc3NpbGUQA0IYWhZzZXJ2ZXIvYXBp",
            "L3BiL3BiX3Rvd2VyYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Article.ArticleReflection.Descriptor, global::PbGameconfig.GameconfigReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Tower.TowerType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.TowerLoadReq), global::Tower.TowerLoadReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.TowerLoadResp), global::Tower.TowerLoadResp.Parser, new[]{ "Towers", "EndAt", "ServerOpenDays" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.TowerChooseReq), global::Tower.TowerChooseReq.Parser, new[]{ "Type", "ChapterId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.TowerChooseResp), global::Tower.TowerChooseResp.Parser, new[]{ "Chapter" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.TowerFightReq), global::Tower.TowerFightReq.Parser, new[]{ "MissionId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.TowerFightResp), global::Tower.TowerFightResp.Parser, new[]{ "Mission", "Rewards", "ReportId", "Heroes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.Tower), global::Tower.Tower.Parser, new[]{ "Type", "MaxChapterId", "CurrChapter", "EntryTimes", "ChallengedChapterIds", "EnteredHeroes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.Chapter), global::Tower.Chapter.Parser, new[]{ "ChapterId", "Missions" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.Mission), global::Tower.Mission.Parser, new[]{ "MissionId", "IsWin" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Tower.UpgradeHero), global::Tower.UpgradeHero.Parser, new[]{ "HeroId", "HeroLevel" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum TowerType {
    /// <summary>
    /// 占位
    /// </summary>
    [pbr::OriginalName("Nil")] Nil = 0,
    /// <summary>
    /// 坦克
    /// </summary>
    [pbr::OriginalName("Tank")] Tank = 1,
    /// <summary>
    /// 飞机
    /// </summary>
    [pbr::OriginalName("Airplane")] Airplane = 2,
    /// <summary>
    /// 导弹
    /// </summary>
    [pbr::OriginalName("Missile")] Missile = 3,
  }

  #endregion

  #region Messages
  /// <summary>
  /// 荣耀远征加载, 5001
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TowerLoadReq : pb::IMessage<TowerLoadReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TowerLoadReq> _parser = new pb::MessageParser<TowerLoadReq>(() => new TowerLoadReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TowerLoadReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerLoadReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerLoadReq(TowerLoadReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerLoadReq Clone() {
      return new TowerLoadReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TowerLoadReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TowerLoadReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TowerLoadReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TowerLoadResp : pb::IMessage<TowerLoadResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TowerLoadResp> _parser = new pb::MessageParser<TowerLoadResp>(() => new TowerLoadResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TowerLoadResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerLoadResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerLoadResp(TowerLoadResp other) : this() {
      towers_ = other.towers_.Clone();
      endAt_ = other.endAt_;
      serverOpenDays_ = other.serverOpenDays_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerLoadResp Clone() {
      return new TowerLoadResp(this);
    }

    /// <summary>Field number for the "towers" field.</summary>
    public const int TowersFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Tower.Tower> _repeated_towers_codec
        = pb::FieldCodec.ForMessage(10, global::Tower.Tower.Parser);
    private readonly pbc::RepeatedField<global::Tower.Tower> towers_ = new pbc::RepeatedField<global::Tower.Tower>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Tower.Tower> Towers {
      get { return towers_; }
    }

    /// <summary>Field number for the "end_at" field.</summary>
    public const int EndAtFieldNumber = 2;
    private long endAt_;
    /// <summary>
    /// 本期结束时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long EndAt {
      get { return endAt_; }
      set {
        endAt_ = value;
      }
    }

    /// <summary>Field number for the "server_open_days" field.</summary>
    public const int ServerOpenDaysFieldNumber = 3;
    private int serverOpenDays_;
    /// <summary>
    /// 开服天数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ServerOpenDays {
      get { return serverOpenDays_; }
      set {
        serverOpenDays_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TowerLoadResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TowerLoadResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!towers_.Equals(other.towers_)) return false;
      if (EndAt != other.EndAt) return false;
      if (ServerOpenDays != other.ServerOpenDays) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= towers_.GetHashCode();
      if (EndAt != 0L) hash ^= EndAt.GetHashCode();
      if (ServerOpenDays != 0) hash ^= ServerOpenDays.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      towers_.WriteTo(output, _repeated_towers_codec);
      if (EndAt != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(EndAt);
      }
      if (ServerOpenDays != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(ServerOpenDays);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      towers_.WriteTo(ref output, _repeated_towers_codec);
      if (EndAt != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(EndAt);
      }
      if (ServerOpenDays != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(ServerOpenDays);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += towers_.CalculateSize(_repeated_towers_codec);
      if (EndAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(EndAt);
      }
      if (ServerOpenDays != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ServerOpenDays);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TowerLoadResp other) {
      if (other == null) {
        return;
      }
      towers_.Add(other.towers_);
      if (other.EndAt != 0L) {
        EndAt = other.EndAt;
      }
      if (other.ServerOpenDays != 0) {
        ServerOpenDays = other.ServerOpenDays;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            towers_.AddEntriesFrom(input, _repeated_towers_codec);
            break;
          }
          case 16: {
            EndAt = input.ReadInt64();
            break;
          }
          case 24: {
            ServerOpenDays = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            towers_.AddEntriesFrom(ref input, _repeated_towers_codec);
            break;
          }
          case 16: {
            EndAt = input.ReadInt64();
            break;
          }
          case 24: {
            ServerOpenDays = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 荣耀远征选择章节, 5002
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TowerChooseReq : pb::IMessage<TowerChooseReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TowerChooseReq> _parser = new pb::MessageParser<TowerChooseReq>(() => new TowerChooseReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TowerChooseReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerChooseReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerChooseReq(TowerChooseReq other) : this() {
      type_ = other.type_;
      chapterId_ = other.chapterId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerChooseReq Clone() {
      return new TowerChooseReq(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::Tower.TowerType type_ = global::Tower.TowerType.Nil;
    /// <summary>
    /// 类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Tower.TowerType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "chapter_id" field.</summary>
    public const int ChapterIdFieldNumber = 2;
    private int chapterId_;
    /// <summary>
    /// 章节 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ChapterId {
      get { return chapterId_; }
      set {
        chapterId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TowerChooseReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TowerChooseReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (ChapterId != other.ChapterId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::Tower.TowerType.Nil) hash ^= Type.GetHashCode();
      if (ChapterId != 0) hash ^= ChapterId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::Tower.TowerType.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (ChapterId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ChapterId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::Tower.TowerType.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (ChapterId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ChapterId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::Tower.TowerType.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (ChapterId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ChapterId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TowerChooseReq other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::Tower.TowerType.Nil) {
        Type = other.Type;
      }
      if (other.ChapterId != 0) {
        ChapterId = other.ChapterId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::Tower.TowerType) input.ReadEnum();
            break;
          }
          case 16: {
            ChapterId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::Tower.TowerType) input.ReadEnum();
            break;
          }
          case 16: {
            ChapterId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TowerChooseResp : pb::IMessage<TowerChooseResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TowerChooseResp> _parser = new pb::MessageParser<TowerChooseResp>(() => new TowerChooseResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TowerChooseResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerChooseResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerChooseResp(TowerChooseResp other) : this() {
      chapter_ = other.chapter_ != null ? other.chapter_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerChooseResp Clone() {
      return new TowerChooseResp(this);
    }

    /// <summary>Field number for the "chapter" field.</summary>
    public const int ChapterFieldNumber = 1;
    private global::Tower.Chapter chapter_;
    /// <summary>
    /// 章节信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Tower.Chapter Chapter {
      get { return chapter_; }
      set {
        chapter_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TowerChooseResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TowerChooseResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Chapter, other.Chapter)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (chapter_ != null) hash ^= Chapter.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (chapter_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Chapter);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (chapter_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Chapter);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (chapter_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Chapter);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TowerChooseResp other) {
      if (other == null) {
        return;
      }
      if (other.chapter_ != null) {
        if (chapter_ == null) {
          Chapter = new global::Tower.Chapter();
        }
        Chapter.MergeFrom(other.Chapter);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (chapter_ == null) {
              Chapter = new global::Tower.Chapter();
            }
            input.ReadMessage(Chapter);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (chapter_ == null) {
              Chapter = new global::Tower.Chapter();
            }
            input.ReadMessage(Chapter);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 荣耀远征战斗, 5003
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TowerFightReq : pb::IMessage<TowerFightReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TowerFightReq> _parser = new pb::MessageParser<TowerFightReq>(() => new TowerFightReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TowerFightReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerFightReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerFightReq(TowerFightReq other) : this() {
      missionId_ = other.missionId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerFightReq Clone() {
      return new TowerFightReq(this);
    }

    /// <summary>Field number for the "mission_id" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    /// <summary>
    /// 关卡 id, tower_level.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TowerFightReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TowerFightReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TowerFightReq other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TowerFightResp : pb::IMessage<TowerFightResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TowerFightResp> _parser = new pb::MessageParser<TowerFightResp>(() => new TowerFightResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TowerFightResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerFightResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerFightResp(TowerFightResp other) : this() {
      mission_ = other.mission_ != null ? other.mission_.Clone() : null;
      rewards_ = other.rewards_.Clone();
      reportId_ = other.reportId_;
      heroes_ = other.heroes_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TowerFightResp Clone() {
      return new TowerFightResp(this);
    }

    /// <summary>Field number for the "mission" field.</summary>
    public const int MissionFieldNumber = 1;
    private global::Tower.Mission mission_;
    /// <summary>
    /// 关卡
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Tower.Mission Mission {
      get { return mission_; }
      set {
        mission_ = value;
      }
    }

    /// <summary>Field number for the "rewards" field.</summary>
    public const int RewardsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Article.Article> _repeated_rewards_codec
        = pb::FieldCodec.ForMessage(18, global::Article.Article.Parser);
    private readonly pbc::RepeatedField<global::Article.Article> rewards_ = new pbc::RepeatedField<global::Article.Article>();
    /// <summary>
    /// 奖励, 如果是快速战斗，客户端需要缓存起来
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Article.Article> Rewards {
      get { return rewards_; }
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 3;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "heroes" field.</summary>
    public const int HeroesFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Tower.UpgradeHero> _repeated_heroes_codec
        = pb::FieldCodec.ForMessage(34, global::Tower.UpgradeHero.Parser);
    private readonly pbc::RepeatedField<global::Tower.UpgradeHero> heroes_ = new pbc::RepeatedField<global::Tower.UpgradeHero>();
    /// <summary>
    /// 有概率升级的英雄
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Tower.UpgradeHero> Heroes {
      get { return heroes_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TowerFightResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TowerFightResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Mission, other.Mission)) return false;
      if(!rewards_.Equals(other.rewards_)) return false;
      if (ReportId != other.ReportId) return false;
      if(!heroes_.Equals(other.heroes_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (mission_ != null) hash ^= Mission.GetHashCode();
      hash ^= rewards_.GetHashCode();
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      hash ^= heroes_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (mission_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Mission);
      }
      rewards_.WriteTo(output, _repeated_rewards_codec);
      if (ReportId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(ReportId);
      }
      heroes_.WriteTo(output, _repeated_heroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (mission_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Mission);
      }
      rewards_.WriteTo(ref output, _repeated_rewards_codec);
      if (ReportId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(ReportId);
      }
      heroes_.WriteTo(ref output, _repeated_heroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (mission_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Mission);
      }
      size += rewards_.CalculateSize(_repeated_rewards_codec);
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      size += heroes_.CalculateSize(_repeated_heroes_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TowerFightResp other) {
      if (other == null) {
        return;
      }
      if (other.mission_ != null) {
        if (mission_ == null) {
          Mission = new global::Tower.Mission();
        }
        Mission.MergeFrom(other.Mission);
      }
      rewards_.Add(other.rewards_);
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      heroes_.Add(other.heroes_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (mission_ == null) {
              Mission = new global::Tower.Mission();
            }
            input.ReadMessage(Mission);
            break;
          }
          case 18: {
            rewards_.AddEntriesFrom(input, _repeated_rewards_codec);
            break;
          }
          case 26: {
            ReportId = input.ReadString();
            break;
          }
          case 34: {
            heroes_.AddEntriesFrom(input, _repeated_heroes_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (mission_ == null) {
              Mission = new global::Tower.Mission();
            }
            input.ReadMessage(Mission);
            break;
          }
          case 18: {
            rewards_.AddEntriesFrom(ref input, _repeated_rewards_codec);
            break;
          }
          case 26: {
            ReportId = input.ReadString();
            break;
          }
          case 34: {
            heroes_.AddEntriesFrom(ref input, _repeated_heroes_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Tower : pb::IMessage<Tower>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Tower> _parser = new pb::MessageParser<Tower>(() => new Tower());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Tower> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Tower() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Tower(Tower other) : this() {
      type_ = other.type_;
      maxChapterId_ = other.maxChapterId_;
      currChapter_ = other.currChapter_ != null ? other.currChapter_.Clone() : null;
      entryTimes_ = other.entryTimes_;
      challengedChapterIds_ = other.challengedChapterIds_.Clone();
      enteredHeroes_ = other.enteredHeroes_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Tower Clone() {
      return new Tower(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::Tower.TowerType type_ = global::Tower.TowerType.Nil;
    /// <summary>
    /// 类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Tower.TowerType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "max_chapter_id" field.</summary>
    public const int MaxChapterIdFieldNumber = 2;
    private int maxChapterId_;
    /// <summary>
    /// 最大已通关章节, tower_level.xlsx - chapter
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MaxChapterId {
      get { return maxChapterId_; }
      set {
        maxChapterId_ = value;
      }
    }

    /// <summary>Field number for the "curr_chapter" field.</summary>
    public const int CurrChapterFieldNumber = 3;
    private global::Tower.Chapter currChapter_;
    /// <summary>
    /// 本周期当前正在挑战的关卡, 可能为空
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Tower.Chapter CurrChapter {
      get { return currChapter_; }
      set {
        currChapter_ = value;
      }
    }

    /// <summary>Field number for the "entry_times" field.</summary>
    public const int EntryTimesFieldNumber = 4;
    private int entryTimes_;
    /// <summary>
    /// 已进入次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int EntryTimes {
      get { return entryTimes_; }
      set {
        entryTimes_ = value;
      }
    }

    /// <summary>Field number for the "challenged_chapter_ids" field.</summary>
    public const int ChallengedChapterIdsFieldNumber = 5;
    private static readonly pb::FieldCodec<int> _repeated_challengedChapterIds_codec
        = pb::FieldCodec.ForInt32(42);
    private readonly pbc::RepeatedField<int> challengedChapterIds_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// 已挑战章节
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> ChallengedChapterIds {
      get { return challengedChapterIds_; }
    }

    /// <summary>Field number for the "entered_heroes" field.</summary>
    public const int EnteredHeroesFieldNumber = 6;
    private static readonly pb::FieldCodec<int> _repeated_enteredHeroes_codec
        = pb::FieldCodec.ForInt32(50);
    private readonly pbc::RepeatedField<int> enteredHeroes_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// 已进入战斗的英雄
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> EnteredHeroes {
      get { return enteredHeroes_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Tower);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Tower other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (MaxChapterId != other.MaxChapterId) return false;
      if (!object.Equals(CurrChapter, other.CurrChapter)) return false;
      if (EntryTimes != other.EntryTimes) return false;
      if(!challengedChapterIds_.Equals(other.challengedChapterIds_)) return false;
      if(!enteredHeroes_.Equals(other.enteredHeroes_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::Tower.TowerType.Nil) hash ^= Type.GetHashCode();
      if (MaxChapterId != 0) hash ^= MaxChapterId.GetHashCode();
      if (currChapter_ != null) hash ^= CurrChapter.GetHashCode();
      if (EntryTimes != 0) hash ^= EntryTimes.GetHashCode();
      hash ^= challengedChapterIds_.GetHashCode();
      hash ^= enteredHeroes_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::Tower.TowerType.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (MaxChapterId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MaxChapterId);
      }
      if (currChapter_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(CurrChapter);
      }
      if (EntryTimes != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(EntryTimes);
      }
      challengedChapterIds_.WriteTo(output, _repeated_challengedChapterIds_codec);
      enteredHeroes_.WriteTo(output, _repeated_enteredHeroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::Tower.TowerType.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (MaxChapterId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MaxChapterId);
      }
      if (currChapter_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(CurrChapter);
      }
      if (EntryTimes != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(EntryTimes);
      }
      challengedChapterIds_.WriteTo(ref output, _repeated_challengedChapterIds_codec);
      enteredHeroes_.WriteTo(ref output, _repeated_enteredHeroes_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::Tower.TowerType.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (MaxChapterId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MaxChapterId);
      }
      if (currChapter_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(CurrChapter);
      }
      if (EntryTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(EntryTimes);
      }
      size += challengedChapterIds_.CalculateSize(_repeated_challengedChapterIds_codec);
      size += enteredHeroes_.CalculateSize(_repeated_enteredHeroes_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Tower other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::Tower.TowerType.Nil) {
        Type = other.Type;
      }
      if (other.MaxChapterId != 0) {
        MaxChapterId = other.MaxChapterId;
      }
      if (other.currChapter_ != null) {
        if (currChapter_ == null) {
          CurrChapter = new global::Tower.Chapter();
        }
        CurrChapter.MergeFrom(other.CurrChapter);
      }
      if (other.EntryTimes != 0) {
        EntryTimes = other.EntryTimes;
      }
      challengedChapterIds_.Add(other.challengedChapterIds_);
      enteredHeroes_.Add(other.enteredHeroes_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::Tower.TowerType) input.ReadEnum();
            break;
          }
          case 16: {
            MaxChapterId = input.ReadInt32();
            break;
          }
          case 26: {
            if (currChapter_ == null) {
              CurrChapter = new global::Tower.Chapter();
            }
            input.ReadMessage(CurrChapter);
            break;
          }
          case 32: {
            EntryTimes = input.ReadInt32();
            break;
          }
          case 42:
          case 40: {
            challengedChapterIds_.AddEntriesFrom(input, _repeated_challengedChapterIds_codec);
            break;
          }
          case 50:
          case 48: {
            enteredHeroes_.AddEntriesFrom(input, _repeated_enteredHeroes_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::Tower.TowerType) input.ReadEnum();
            break;
          }
          case 16: {
            MaxChapterId = input.ReadInt32();
            break;
          }
          case 26: {
            if (currChapter_ == null) {
              CurrChapter = new global::Tower.Chapter();
            }
            input.ReadMessage(CurrChapter);
            break;
          }
          case 32: {
            EntryTimes = input.ReadInt32();
            break;
          }
          case 42:
          case 40: {
            challengedChapterIds_.AddEntriesFrom(ref input, _repeated_challengedChapterIds_codec);
            break;
          }
          case 50:
          case 48: {
            enteredHeroes_.AddEntriesFrom(ref input, _repeated_enteredHeroes_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 章节
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Chapter : pb::IMessage<Chapter>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Chapter> _parser = new pb::MessageParser<Chapter>(() => new Chapter());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Chapter> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Chapter() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Chapter(Chapter other) : this() {
      chapterId_ = other.chapterId_;
      missions_ = other.missions_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Chapter Clone() {
      return new Chapter(this);
    }

    /// <summary>Field number for the "chapter_id" field.</summary>
    public const int ChapterIdFieldNumber = 1;
    private int chapterId_;
    /// <summary>
    /// 章节 id, tower_level.xlsx - chapter
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ChapterId {
      get { return chapterId_; }
      set {
        chapterId_ = value;
      }
    }

    /// <summary>Field number for the "missions" field.</summary>
    public const int MissionsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Tower.Mission> _repeated_missions_codec
        = pb::FieldCodec.ForMessage(18, global::Tower.Mission.Parser);
    private readonly pbc::RepeatedField<global::Tower.Mission> missions_ = new pbc::RepeatedField<global::Tower.Mission>();
    /// <summary>
    /// 已挑战的关卡
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Tower.Mission> Missions {
      get { return missions_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Chapter);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Chapter other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ChapterId != other.ChapterId) return false;
      if(!missions_.Equals(other.missions_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ChapterId != 0) hash ^= ChapterId.GetHashCode();
      hash ^= missions_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ChapterId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ChapterId);
      }
      missions_.WriteTo(output, _repeated_missions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ChapterId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ChapterId);
      }
      missions_.WriteTo(ref output, _repeated_missions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ChapterId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ChapterId);
      }
      size += missions_.CalculateSize(_repeated_missions_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Chapter other) {
      if (other == null) {
        return;
      }
      if (other.ChapterId != 0) {
        ChapterId = other.ChapterId;
      }
      missions_.Add(other.missions_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ChapterId = input.ReadInt32();
            break;
          }
          case 18: {
            missions_.AddEntriesFrom(input, _repeated_missions_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ChapterId = input.ReadInt32();
            break;
          }
          case 18: {
            missions_.AddEntriesFrom(ref input, _repeated_missions_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 章节-关卡
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Mission : pb::IMessage<Mission>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Mission> _parser = new pb::MessageParser<Mission>(() => new Mission());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Mission> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Mission() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Mission(Mission other) : this() {
      missionId_ = other.missionId_;
      isWin_ = other.isWin_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Mission Clone() {
      return new Mission(this);
    }

    /// <summary>Field number for the "mission_id" field.</summary>
    public const int MissionIdFieldNumber = 1;
    private int missionId_;
    /// <summary>
    /// 关卡 id, tower_level.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MissionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "is_win" field.</summary>
    public const int IsWinFieldNumber = 2;
    private bool isWin_;
    /// <summary>
    /// 是否胜利
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsWin {
      get { return isWin_; }
      set {
        isWin_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Mission);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Mission other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MissionId != other.MissionId) return false;
      if (IsWin != other.IsWin) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MissionId != 0) hash ^= MissionId.GetHashCode();
      if (IsWin != false) hash ^= IsWin.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (IsWin != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsWin);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MissionId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MissionId);
      }
      if (IsWin != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsWin);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MissionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MissionId);
      }
      if (IsWin != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Mission other) {
      if (other == null) {
        return;
      }
      if (other.MissionId != 0) {
        MissionId = other.MissionId;
      }
      if (other.IsWin != false) {
        IsWin = other.IsWin;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            IsWin = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MissionId = input.ReadInt32();
            break;
          }
          case 16: {
            IsWin = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UpgradeHero : pb::IMessage<UpgradeHero>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UpgradeHero> _parser = new pb::MessageParser<UpgradeHero>(() => new UpgradeHero());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UpgradeHero> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Tower.TowerReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpgradeHero() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpgradeHero(UpgradeHero other) : this() {
      heroId_ = other.heroId_;
      heroLevel_ = other.heroLevel_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UpgradeHero Clone() {
      return new UpgradeHero(this);
    }

    /// <summary>Field number for the "hero_id" field.</summary>
    public const int HeroIdFieldNumber = 1;
    private global::PbGameconfig.itemid heroId_ = global::PbGameconfig.itemid.Nil;
    /// <summary>
    /// 英雄 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.itemid HeroId {
      get { return heroId_; }
      set {
        heroId_ = value;
      }
    }

    /// <summary>Field number for the "hero_level" field.</summary>
    public const int HeroLevelFieldNumber = 2;
    private int heroLevel_;
    /// <summary>
    /// 英雄最新等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int HeroLevel {
      get { return heroLevel_; }
      set {
        heroLevel_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UpgradeHero);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UpgradeHero other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (HeroId != other.HeroId) return false;
      if (HeroLevel != other.HeroLevel) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HeroId != global::PbGameconfig.itemid.Nil) hash ^= HeroId.GetHashCode();
      if (HeroLevel != 0) hash ^= HeroLevel.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) HeroId);
      }
      if (HeroLevel != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(HeroLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) HeroId);
      }
      if (HeroLevel != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(HeroLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HeroId != global::PbGameconfig.itemid.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) HeroId);
      }
      if (HeroLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HeroLevel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UpgradeHero other) {
      if (other == null) {
        return;
      }
      if (other.HeroId != global::PbGameconfig.itemid.Nil) {
        HeroId = other.HeroId;
      }
      if (other.HeroLevel != 0) {
        HeroLevel = other.HeroLevel;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            HeroId = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            HeroLevel = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            HeroId = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            HeroLevel = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
