// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: payment.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Payment {

  /// <summary>Holder for reflection information generated from payment.proto</summary>
  public static partial class PaymentReflection {

    #region Descriptor
    /// <summary>File descriptor for payment.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PaymentReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg1wYXltZW50LnByb3RvEgdwYXltZW50Gg1hcnRpY2xlLnByb3RvIpMBCgdQ",
            "YXlsb2FkEhIKCmFjY291bnRfaWQYASABKAQSEQoJc2VydmVyX2lkGAIgASgN",
            "Eg8KB3JvbGVfaWQYAyABKAQSEgoKcHJvZHVjdF9pZBgEIAEoBRILCgNleHQY",
            "BSABKAkSCgoCaWQYBiABKAQSEAoIb3JkZXJfaWQYByABKAkSEQoJY3JlYXRl",
            "X2F0GAggASgDIsYBCg9QYXltZW50T3JkZXJSZXESIQoHY2hhbm5lbBgBIAEo",
            "DjIQLnBheW1lbnQuQ2hhbm5lbBISCgphY2NvdW50X2lkGAIgASgEEhEKCXNl",
            "cnZlcl9pZBgDIAEoDRIPCgdyb2xlX2lkGAQgASgEEiMKCGN1cnJlbmN5GAUg",
            "ASgOMhEucGF5bWVudC5DdXJyZW5jeRIOCgZhbW91bnQYBiABKA0SEgoKcHJv",
            "ZHVjdF9pZBgHIAEoBRIPCgdwYXlsb2FkGAggASgJIjUKEFBheW1lbnRPcmRl",
            "clJlc3ASEAoIb3JkZXJfaWQYASABKAkSDwoHcGF5bG9hZBgCIAEoCSI4ChBQ",
            "YXltZW50UmV3YXJkUmVxEhAKCG9yZGVyX2lkGAEgASgJEhIKCnByb2R1Y3Rf",
            "aWQYAiABKAUiEwoRUGF5bWVudFJld2FyZFJlc3AijQEKD1B1c2hQYXltZW50",
            "UmVzcBISCgpwcm9kdWN0X2lkGAEgASgFEhAKCG9yZGVyX2lkGAIgASgJEhEK",
            "CXZpcF9sZXZlbBgDIAEoDRIPCgd2aXBfZXhwGAQgASgEEg0KBXNjb3JlGAUg",
            "ASgEEiEKB3Jld2FyZHMYBiADKAsyEC5hcnRpY2xlLkFydGljbGUiMgocUGF5",
            "bWVudE9yZGVyQnVzaW5lc3NSdWxlc1JlcRISCgpwcm9kdWN0X2lkGAEgASgF",
            "Ih8KHVBheW1lbnRPcmRlckJ1c2luZXNzUnVsZXNSZXNwKh4KB0NoYW5uZWwS",
            "CAoEVGVzdBAAEgkKBUxvY2FsEAEqSgoLT3JkZXJTdGF0dXMSCwoHUEVORElO",
            "RxAAEggKBFBBSUQQARINCglGVUxGSUxMRUQQAhIJCgVSRVRSWRADEgoKBlJF",
            "RlVORBAEKsEBCghDdXJyZW5jeRIPCgtDdXJyZW5jeU5pbBAAEgcKA0NOWRAB",
            "EgcKA1VTRBACEgcKA0dCUBADEgcKA0VVUhAEEgcKA0pQWRAFEgcKA0FVRBAG",
            "EgcKA0NBRBAHEgcKA0NIRhAIEgcKA0hLRBAJEgcKA1NHRBAKEgcKA05aRBAL",
            "EgcKA1RIQhAMEgcKA1ZORBANEgcKA0tSVxAOEgcKA1BIUBAPEgcKA0lEUhAQ",
            "EgcKA1JVQhAREgsKBlRva2VuMRDpB0IaWhhzZXJ2ZXIvYXBpL3BiL3BiX3Bh",
            "eW1lbnRiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Article.ArticleReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Payment.Channel), typeof(global::Payment.OrderStatus), typeof(global::Payment.Currency), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.Payload), global::Payment.Payload.Parser, new[]{ "AccountId", "ServerId", "RoleId", "ProductId", "Ext", "Id", "OrderId", "CreateAt" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.PaymentOrderReq), global::Payment.PaymentOrderReq.Parser, new[]{ "Channel", "AccountId", "ServerId", "RoleId", "Currency", "Amount", "ProductId", "Payload" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.PaymentOrderResp), global::Payment.PaymentOrderResp.Parser, new[]{ "OrderId", "Payload" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.PaymentRewardReq), global::Payment.PaymentRewardReq.Parser, new[]{ "OrderId", "ProductId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.PaymentRewardResp), global::Payment.PaymentRewardResp.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.PushPaymentResp), global::Payment.PushPaymentResp.Parser, new[]{ "ProductId", "OrderId", "VipLevel", "VipExp", "Score", "Rewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.PaymentOrderBusinessRulesReq), global::Payment.PaymentOrderBusinessRulesReq.Parser, new[]{ "ProductId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Payment.PaymentOrderBusinessRulesResp), global::Payment.PaymentOrderBusinessRulesResp.Parser, null, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 充值渠道
  /// </summary>
  public enum Channel {
    /// <summary>
    /// 测试使用
    /// </summary>
    [pbr::OriginalName("Test")] Test = 0,
    /// <summary>
    /// 本地模拟支付
    /// </summary>
    [pbr::OriginalName("Local")] Local = 1,
  }

  /// <summary>
  /// 订单状态
  /// </summary>
  public enum OrderStatus {
    /// <summary>
    /// 已下单,未支付
    /// </summary>
    [pbr::OriginalName("PENDING")] Pending = 0,
    /// <summary>
    /// 已支付,未发货
    /// </summary>
    [pbr::OriginalName("PAID")] Paid = 1,
    /// <summary>
    /// 已支付,已发货
    /// </summary>
    [pbr::OriginalName("FULFILLED")] Fulfilled = 2,
    /// <summary>
    /// 已支付,尝试发货次数超过上限
    /// </summary>
    [pbr::OriginalName("RETRY")] Retry = 3,
    /// <summary>
    /// 已退款
    /// </summary>
    [pbr::OriginalName("REFUND")] Refund = 4,
  }

  /// <summary>
  /// 货币类型
  /// </summary>
  public enum Currency {
    /// <summary>
    /// 无货币
    /// </summary>
    [pbr::OriginalName("CurrencyNil")] Nil = 0,
    /// <summary>
    /// 人民币
    /// </summary>
    [pbr::OriginalName("CNY")] Cny = 1,
    /// <summary>
    /// 美元
    /// </summary>
    [pbr::OriginalName("USD")] Usd = 2,
    /// <summary>
    /// 英镑
    /// </summary>
    [pbr::OriginalName("GBP")] Gbp = 3,
    /// <summary>
    /// 欧元
    /// </summary>
    [pbr::OriginalName("EUR")] Eur = 4,
    /// <summary>
    /// 日元
    /// </summary>
    [pbr::OriginalName("JPY")] Jpy = 5,
    /// <summary>
    /// 澳元
    /// </summary>
    [pbr::OriginalName("AUD")] Aud = 6,
    /// <summary>
    /// 加元
    /// </summary>
    [pbr::OriginalName("CAD")] Cad = 7,
    /// <summary>
    /// 瑞士法郎
    /// </summary>
    [pbr::OriginalName("CHF")] Chf = 8,
    /// <summary>
    /// 港币
    /// </summary>
    [pbr::OriginalName("HKD")] Hkd = 9,
    /// <summary>
    /// 新加坡元
    /// </summary>
    [pbr::OriginalName("SGD")] Sgd = 10,
    /// <summary>
    /// 新西兰元
    /// </summary>
    [pbr::OriginalName("NZD")] Nzd = 11,
    /// <summary>
    /// 泰铢
    /// </summary>
    [pbr::OriginalName("THB")] Thb = 12,
    /// <summary>
    /// 越南盾
    /// </summary>
    [pbr::OriginalName("VND")] Vnd = 13,
    /// <summary>
    /// 韩元
    /// </summary>
    [pbr::OriginalName("KRW")] Krw = 14,
    /// <summary>
    /// 菲律宾比索
    /// </summary>
    [pbr::OriginalName("PHP")] Php = 15,
    /// <summary>
    /// 印度尼西亚盾
    /// </summary>
    [pbr::OriginalName("IDR")] Idr = 16,
    /// <summary>
    /// 卢布
    /// </summary>
    [pbr::OriginalName("RUB")] Rub = 17,
    /// <summary>
    /// 代币价格(金砖)
    /// </summary>
    [pbr::OriginalName("Token1")] Token1 = 1001,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Payload : pb::IMessage<Payload>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Payload> _parser = new pb::MessageParser<Payload>(() => new Payload());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Payload> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Payload() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Payload(Payload other) : this() {
      accountId_ = other.accountId_;
      serverId_ = other.serverId_;
      roleId_ = other.roleId_;
      productId_ = other.productId_;
      ext_ = other.ext_;
      id_ = other.id_;
      orderId_ = other.orderId_;
      createAt_ = other.createAt_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Payload Clone() {
      return new Payload(this);
    }

    /// <summary>Field number for the "account_id" field.</summary>
    public const int AccountIdFieldNumber = 1;
    private ulong accountId_;
    /// <summary>
    /// 账号 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong AccountId {
      get { return accountId_; }
      set {
        accountId_ = value;
      }
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 2;
    private uint serverId_;
    /// <summary>
    /// 服务器 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 3;
    private ulong roleId_;
    /// <summary>
    /// 角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int ProductIdFieldNumber = 4;
    private int productId_;
    /// <summary>
    /// 产品 id, payment.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ProductId {
      get { return productId_; }
      set {
        productId_ = value;
      }
    }

    /// <summary>Field number for the "ext" field.</summary>
    public const int ExtFieldNumber = 5;
    private string ext_ = "";
    /// <summary>
    /// 客户端透传参数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Ext {
      get { return ext_; }
      set {
        ext_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 6;
    private ulong id_;
    /// <summary>
    /// 订单自增 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "order_id" field.</summary>
    public const int OrderIdFieldNumber = 7;
    private string orderId_ = "";
    /// <summary>
    /// 订单号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OrderId {
      get { return orderId_; }
      set {
        orderId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "create_at" field.</summary>
    public const int CreateAtFieldNumber = 8;
    private long createAt_;
    /// <summary>
    /// 订单创建时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CreateAt {
      get { return createAt_; }
      set {
        createAt_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Payload);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Payload other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AccountId != other.AccountId) return false;
      if (ServerId != other.ServerId) return false;
      if (RoleId != other.RoleId) return false;
      if (ProductId != other.ProductId) return false;
      if (Ext != other.Ext) return false;
      if (Id != other.Id) return false;
      if (OrderId != other.OrderId) return false;
      if (CreateAt != other.CreateAt) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (AccountId != 0UL) hash ^= AccountId.GetHashCode();
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      if (ProductId != 0) hash ^= ProductId.GetHashCode();
      if (Ext.Length != 0) hash ^= Ext.GetHashCode();
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (OrderId.Length != 0) hash ^= OrderId.GetHashCode();
      if (CreateAt != 0L) hash ^= CreateAt.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (AccountId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(AccountId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(ServerId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(RoleId);
      }
      if (ProductId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(ProductId);
      }
      if (Ext.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Ext);
      }
      if (Id != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(Id);
      }
      if (OrderId.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(OrderId);
      }
      if (CreateAt != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(CreateAt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (AccountId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(AccountId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(ServerId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(RoleId);
      }
      if (ProductId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(ProductId);
      }
      if (Ext.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Ext);
      }
      if (Id != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(Id);
      }
      if (OrderId.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(OrderId);
      }
      if (CreateAt != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(CreateAt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (AccountId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(AccountId);
      }
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      if (ProductId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ProductId);
      }
      if (Ext.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Ext);
      }
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (OrderId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(OrderId);
      }
      if (CreateAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CreateAt);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Payload other) {
      if (other == null) {
        return;
      }
      if (other.AccountId != 0UL) {
        AccountId = other.AccountId;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      if (other.ProductId != 0) {
        ProductId = other.ProductId;
      }
      if (other.Ext.Length != 0) {
        Ext = other.Ext;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.OrderId.Length != 0) {
        OrderId = other.OrderId;
      }
      if (other.CreateAt != 0L) {
        CreateAt = other.CreateAt;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AccountId = input.ReadUInt64();
            break;
          }
          case 16: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 24: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 32: {
            ProductId = input.ReadInt32();
            break;
          }
          case 42: {
            Ext = input.ReadString();
            break;
          }
          case 48: {
            Id = input.ReadUInt64();
            break;
          }
          case 58: {
            OrderId = input.ReadString();
            break;
          }
          case 64: {
            CreateAt = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            AccountId = input.ReadUInt64();
            break;
          }
          case 16: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 24: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 32: {
            ProductId = input.ReadInt32();
            break;
          }
          case 42: {
            Ext = input.ReadString();
            break;
          }
          case 48: {
            Id = input.ReadUInt64();
            break;
          }
          case 58: {
            OrderId = input.ReadString();
            break;
          }
          case 64: {
            CreateAt = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 充值下单 40001
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PaymentOrderReq : pb::IMessage<PaymentOrderReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PaymentOrderReq> _parser = new pb::MessageParser<PaymentOrderReq>(() => new PaymentOrderReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PaymentOrderReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderReq(PaymentOrderReq other) : this() {
      channel_ = other.channel_;
      accountId_ = other.accountId_;
      serverId_ = other.serverId_;
      roleId_ = other.roleId_;
      currency_ = other.currency_;
      amount_ = other.amount_;
      productId_ = other.productId_;
      payload_ = other.payload_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderReq Clone() {
      return new PaymentOrderReq(this);
    }

    /// <summary>Field number for the "channel" field.</summary>
    public const int ChannelFieldNumber = 1;
    private global::Payment.Channel channel_ = global::Payment.Channel.Test;
    /// <summary>
    /// 支付渠道
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Payment.Channel Channel {
      get { return channel_; }
      set {
        channel_ = value;
      }
    }

    /// <summary>Field number for the "account_id" field.</summary>
    public const int AccountIdFieldNumber = 2;
    private ulong accountId_;
    /// <summary>
    /// 账号 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong AccountId {
      get { return accountId_; }
      set {
        accountId_ = value;
      }
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 3;
    private uint serverId_;
    /// <summary>
    /// 服务器 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "role_id" field.</summary>
    public const int RoleIdFieldNumber = 4;
    private ulong roleId_;
    /// <summary>
    /// 角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong RoleId {
      get { return roleId_; }
      set {
        roleId_ = value;
      }
    }

    /// <summary>Field number for the "currency" field.</summary>
    public const int CurrencyFieldNumber = 5;
    private global::Payment.Currency currency_ = global::Payment.Currency.Nil;
    /// <summary>
    /// 货币类型 
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Payment.Currency Currency {
      get { return currency_; }
      set {
        currency_ = value;
      }
    }

    /// <summary>Field number for the "amount" field.</summary>
    public const int AmountFieldNumber = 6;
    private uint amount_;
    /// <summary>
    /// 金额,分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Amount {
      get { return amount_; }
      set {
        amount_ = value;
      }
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int ProductIdFieldNumber = 7;
    private int productId_;
    /// <summary>
    /// 产品 id, payment.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ProductId {
      get { return productId_; }
      set {
        productId_ = value;
      }
    }

    /// <summary>Field number for the "payload" field.</summary>
    public const int PayloadFieldNumber = 8;
    private string payload_ = "";
    /// <summary>
    /// 透传参数,没有则填空字符串
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Payload {
      get { return payload_; }
      set {
        payload_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PaymentOrderReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PaymentOrderReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Channel != other.Channel) return false;
      if (AccountId != other.AccountId) return false;
      if (ServerId != other.ServerId) return false;
      if (RoleId != other.RoleId) return false;
      if (Currency != other.Currency) return false;
      if (Amount != other.Amount) return false;
      if (ProductId != other.ProductId) return false;
      if (Payload != other.Payload) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Channel != global::Payment.Channel.Test) hash ^= Channel.GetHashCode();
      if (AccountId != 0UL) hash ^= AccountId.GetHashCode();
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (RoleId != 0UL) hash ^= RoleId.GetHashCode();
      if (Currency != global::Payment.Currency.Nil) hash ^= Currency.GetHashCode();
      if (Amount != 0) hash ^= Amount.GetHashCode();
      if (ProductId != 0) hash ^= ProductId.GetHashCode();
      if (Payload.Length != 0) hash ^= Payload.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Channel != global::Payment.Channel.Test) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Channel);
      }
      if (AccountId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(AccountId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(RoleId);
      }
      if (Currency != global::Payment.Currency.Nil) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Currency);
      }
      if (Amount != 0) {
        output.WriteRawTag(48);
        output.WriteUInt32(Amount);
      }
      if (ProductId != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(ProductId);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Channel != global::Payment.Channel.Test) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Channel);
      }
      if (AccountId != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(AccountId);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (RoleId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(RoleId);
      }
      if (Currency != global::Payment.Currency.Nil) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Currency);
      }
      if (Amount != 0) {
        output.WriteRawTag(48);
        output.WriteUInt32(Amount);
      }
      if (ProductId != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(ProductId);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Channel != global::Payment.Channel.Test) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Channel);
      }
      if (AccountId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(AccountId);
      }
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (RoleId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(RoleId);
      }
      if (Currency != global::Payment.Currency.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Currency);
      }
      if (Amount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Amount);
      }
      if (ProductId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ProductId);
      }
      if (Payload.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Payload);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PaymentOrderReq other) {
      if (other == null) {
        return;
      }
      if (other.Channel != global::Payment.Channel.Test) {
        Channel = other.Channel;
      }
      if (other.AccountId != 0UL) {
        AccountId = other.AccountId;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.RoleId != 0UL) {
        RoleId = other.RoleId;
      }
      if (other.Currency != global::Payment.Currency.Nil) {
        Currency = other.Currency;
      }
      if (other.Amount != 0) {
        Amount = other.Amount;
      }
      if (other.ProductId != 0) {
        ProductId = other.ProductId;
      }
      if (other.Payload.Length != 0) {
        Payload = other.Payload;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Channel = (global::Payment.Channel) input.ReadEnum();
            break;
          }
          case 16: {
            AccountId = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 32: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 40: {
            Currency = (global::Payment.Currency) input.ReadEnum();
            break;
          }
          case 48: {
            Amount = input.ReadUInt32();
            break;
          }
          case 56: {
            ProductId = input.ReadInt32();
            break;
          }
          case 66: {
            Payload = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Channel = (global::Payment.Channel) input.ReadEnum();
            break;
          }
          case 16: {
            AccountId = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 32: {
            RoleId = input.ReadUInt64();
            break;
          }
          case 40: {
            Currency = (global::Payment.Currency) input.ReadEnum();
            break;
          }
          case 48: {
            Amount = input.ReadUInt32();
            break;
          }
          case 56: {
            ProductId = input.ReadInt32();
            break;
          }
          case 66: {
            Payload = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PaymentOrderResp : pb::IMessage<PaymentOrderResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PaymentOrderResp> _parser = new pb::MessageParser<PaymentOrderResp>(() => new PaymentOrderResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PaymentOrderResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderResp(PaymentOrderResp other) : this() {
      orderId_ = other.orderId_;
      payload_ = other.payload_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderResp Clone() {
      return new PaymentOrderResp(this);
    }

    /// <summary>Field number for the "order_id" field.</summary>
    public const int OrderIdFieldNumber = 1;
    private string orderId_ = "";
    /// <summary>
    /// 唯一订单号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OrderId {
      get { return orderId_; }
      set {
        orderId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "payload" field.</summary>
    public const int PayloadFieldNumber = 2;
    private string payload_ = "";
    /// <summary>
    /// 透传参数,一定不为空,发送给充值 sdk
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Payload {
      get { return payload_; }
      set {
        payload_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PaymentOrderResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PaymentOrderResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (OrderId != other.OrderId) return false;
      if (Payload != other.Payload) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (OrderId.Length != 0) hash ^= OrderId.GetHashCode();
      if (Payload.Length != 0) hash ^= Payload.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (OrderId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(OrderId);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (OrderId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(OrderId);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (OrderId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(OrderId);
      }
      if (Payload.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Payload);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PaymentOrderResp other) {
      if (other == null) {
        return;
      }
      if (other.OrderId.Length != 0) {
        OrderId = other.OrderId;
      }
      if (other.Payload.Length != 0) {
        Payload = other.Payload;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            OrderId = input.ReadString();
            break;
          }
          case 18: {
            Payload = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            OrderId = input.ReadString();
            break;
          }
          case 18: {
            Payload = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 充值发放奖励 3201
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PaymentRewardReq : pb::IMessage<PaymentRewardReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PaymentRewardReq> _parser = new pb::MessageParser<PaymentRewardReq>(() => new PaymentRewardReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PaymentRewardReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentRewardReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentRewardReq(PaymentRewardReq other) : this() {
      orderId_ = other.orderId_;
      productId_ = other.productId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentRewardReq Clone() {
      return new PaymentRewardReq(this);
    }

    /// <summary>Field number for the "order_id" field.</summary>
    public const int OrderIdFieldNumber = 1;
    private string orderId_ = "";
    /// <summary>
    /// 订单号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OrderId {
      get { return orderId_; }
      set {
        orderId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int ProductIdFieldNumber = 2;
    private int productId_;
    /// <summary>
    /// 产品 id, payment.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ProductId {
      get { return productId_; }
      set {
        productId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PaymentRewardReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PaymentRewardReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (OrderId != other.OrderId) return false;
      if (ProductId != other.ProductId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (OrderId.Length != 0) hash ^= OrderId.GetHashCode();
      if (ProductId != 0) hash ^= ProductId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (OrderId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(OrderId);
      }
      if (ProductId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ProductId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (OrderId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(OrderId);
      }
      if (ProductId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ProductId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (OrderId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(OrderId);
      }
      if (ProductId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ProductId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PaymentRewardReq other) {
      if (other == null) {
        return;
      }
      if (other.OrderId.Length != 0) {
        OrderId = other.OrderId;
      }
      if (other.ProductId != 0) {
        ProductId = other.ProductId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            OrderId = input.ReadString();
            break;
          }
          case 16: {
            ProductId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            OrderId = input.ReadString();
            break;
          }
          case 16: {
            ProductId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PaymentRewardResp : pb::IMessage<PaymentRewardResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PaymentRewardResp> _parser = new pb::MessageParser<PaymentRewardResp>(() => new PaymentRewardResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PaymentRewardResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentRewardResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentRewardResp(PaymentRewardResp other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentRewardResp Clone() {
      return new PaymentRewardResp(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PaymentRewardResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PaymentRewardResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PaymentRewardResp other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 充值订单完成 3202
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushPaymentResp : pb::IMessage<PushPaymentResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushPaymentResp> _parser = new pb::MessageParser<PushPaymentResp>(() => new PushPaymentResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushPaymentResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushPaymentResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushPaymentResp(PushPaymentResp other) : this() {
      productId_ = other.productId_;
      orderId_ = other.orderId_;
      vipLevel_ = other.vipLevel_;
      vipExp_ = other.vipExp_;
      score_ = other.score_;
      rewards_ = other.rewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushPaymentResp Clone() {
      return new PushPaymentResp(this);
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int ProductIdFieldNumber = 1;
    private int productId_;
    /// <summary>
    /// 产品 id, payment.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ProductId {
      get { return productId_; }
      set {
        productId_ = value;
      }
    }

    /// <summary>Field number for the "order_id" field.</summary>
    public const int OrderIdFieldNumber = 2;
    private string orderId_ = "";
    /// <summary>
    /// 订单号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string OrderId {
      get { return orderId_; }
      set {
        orderId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "vip_level" field.</summary>
    public const int VipLevelFieldNumber = 3;
    private uint vipLevel_;
    /// <summary>
    /// 充值完成后角色的 vip 等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint VipLevel {
      get { return vipLevel_; }
      set {
        vipLevel_ = value;
      }
    }

    /// <summary>Field number for the "vip_exp" field.</summary>
    public const int VipExpFieldNumber = 4;
    private ulong vipExp_;
    /// <summary>
    /// 充值完成后角色的 vip 经验
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong VipExp {
      get { return vipExp_; }
      set {
        vipExp_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int ScoreFieldNumber = 5;
    private ulong score_;
    /// <summary>
    /// 当前充值积分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    /// <summary>Field number for the "rewards" field.</summary>
    public const int RewardsFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Article.Article> _repeated_rewards_codec
        = pb::FieldCodec.ForMessage(50, global::Article.Article.Parser);
    private readonly pbc::RepeatedField<global::Article.Article> rewards_ = new pbc::RepeatedField<global::Article.Article>();
    /// <summary>
    /// 充值完成后的奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Article.Article> Rewards {
      get { return rewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushPaymentResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushPaymentResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ProductId != other.ProductId) return false;
      if (OrderId != other.OrderId) return false;
      if (VipLevel != other.VipLevel) return false;
      if (VipExp != other.VipExp) return false;
      if (Score != other.Score) return false;
      if(!rewards_.Equals(other.rewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ProductId != 0) hash ^= ProductId.GetHashCode();
      if (OrderId.Length != 0) hash ^= OrderId.GetHashCode();
      if (VipLevel != 0) hash ^= VipLevel.GetHashCode();
      if (VipExp != 0UL) hash ^= VipExp.GetHashCode();
      if (Score != 0UL) hash ^= Score.GetHashCode();
      hash ^= rewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ProductId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ProductId);
      }
      if (OrderId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(OrderId);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(VipLevel);
      }
      if (VipExp != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(VipExp);
      }
      if (Score != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(Score);
      }
      rewards_.WriteTo(output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ProductId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ProductId);
      }
      if (OrderId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(OrderId);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(VipLevel);
      }
      if (VipExp != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(VipExp);
      }
      if (Score != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(Score);
      }
      rewards_.WriteTo(ref output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ProductId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ProductId);
      }
      if (OrderId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(OrderId);
      }
      if (VipLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(VipLevel);
      }
      if (VipExp != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(VipExp);
      }
      if (Score != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Score);
      }
      size += rewards_.CalculateSize(_repeated_rewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushPaymentResp other) {
      if (other == null) {
        return;
      }
      if (other.ProductId != 0) {
        ProductId = other.ProductId;
      }
      if (other.OrderId.Length != 0) {
        OrderId = other.OrderId;
      }
      if (other.VipLevel != 0) {
        VipLevel = other.VipLevel;
      }
      if (other.VipExp != 0UL) {
        VipExp = other.VipExp;
      }
      if (other.Score != 0UL) {
        Score = other.Score;
      }
      rewards_.Add(other.rewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ProductId = input.ReadInt32();
            break;
          }
          case 18: {
            OrderId = input.ReadString();
            break;
          }
          case 24: {
            VipLevel = input.ReadUInt32();
            break;
          }
          case 32: {
            VipExp = input.ReadUInt64();
            break;
          }
          case 40: {
            Score = input.ReadUInt64();
            break;
          }
          case 50: {
            rewards_.AddEntriesFrom(input, _repeated_rewards_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ProductId = input.ReadInt32();
            break;
          }
          case 18: {
            OrderId = input.ReadString();
            break;
          }
          case 24: {
            VipLevel = input.ReadUInt32();
            break;
          }
          case 32: {
            VipExp = input.ReadUInt64();
            break;
          }
          case 40: {
            Score = input.ReadUInt64();
            break;
          }
          case 50: {
            rewards_.AddEntriesFrom(ref input, _repeated_rewards_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 充值下单前业务检查 3203
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PaymentOrderBusinessRulesReq : pb::IMessage<PaymentOrderBusinessRulesReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PaymentOrderBusinessRulesReq> _parser = new pb::MessageParser<PaymentOrderBusinessRulesReq>(() => new PaymentOrderBusinessRulesReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PaymentOrderBusinessRulesReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderBusinessRulesReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderBusinessRulesReq(PaymentOrderBusinessRulesReq other) : this() {
      productId_ = other.productId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderBusinessRulesReq Clone() {
      return new PaymentOrderBusinessRulesReq(this);
    }

    /// <summary>Field number for the "product_id" field.</summary>
    public const int ProductIdFieldNumber = 1;
    private int productId_;
    /// <summary>
    /// 产品 id, payment.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ProductId {
      get { return productId_; }
      set {
        productId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PaymentOrderBusinessRulesReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PaymentOrderBusinessRulesReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ProductId != other.ProductId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ProductId != 0) hash ^= ProductId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ProductId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ProductId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ProductId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ProductId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ProductId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ProductId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PaymentOrderBusinessRulesReq other) {
      if (other == null) {
        return;
      }
      if (other.ProductId != 0) {
        ProductId = other.ProductId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ProductId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ProductId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PaymentOrderBusinessRulesResp : pb::IMessage<PaymentOrderBusinessRulesResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PaymentOrderBusinessRulesResp> _parser = new pb::MessageParser<PaymentOrderBusinessRulesResp>(() => new PaymentOrderBusinessRulesResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PaymentOrderBusinessRulesResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Payment.PaymentReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderBusinessRulesResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderBusinessRulesResp(PaymentOrderBusinessRulesResp other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PaymentOrderBusinessRulesResp Clone() {
      return new PaymentOrderBusinessRulesResp(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PaymentOrderBusinessRulesResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PaymentOrderBusinessRulesResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PaymentOrderBusinessRulesResp other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
