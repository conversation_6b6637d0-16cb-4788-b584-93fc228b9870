// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: attr.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Attr {

  /// <summary>Holder for reflection information generated from attr.proto</summary>
  public static partial class AttrReflection {

    #region Descriptor
    /// <summary>File descriptor for attr.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static AttrReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CgphdHRyLnByb3RvEgRhdHRyGhBnYW1lY29uZmlnLnByb3RvIrQBCghBZGRG",
            "aWVsZBIxCglhdHRyX3R5cGUYASABKA4yHi5wYl9nYW1lY29uZmlnLmF0dHJp",
            "YnV0ZXNfdHlwZRISCgphdHRyX3ZhbHVlGAIgASgDEioKCGFkZF90eXBlGAMg",
            "ASgOMhgucGJfZ2FtZWNvbmZpZy52YWx1ZXR5cGUSNQoLdGFyZ2V0X3R5cGUY",
            "BCABKA4yIC5wYl9nYW1lY29uZmlnLmF0dHJpYnV0ZXNfdGFyZ2V0Im4KDU1v",
            "ZHVsZUF0dHJBZGQSDAoEbmFtZRgBIAEoCRIvCgZzb3VyY2UYAiABKA4yHy5w",
            "Yl9nYW1lY29uZmlnLmF0dHJpYnV0ZXNzb3VyY2USHgoGZmllbGRzGAMgAygL",
            "Mg4uYXR0ci5BZGRGaWVsZEIXWhVzZXJ2ZXIvYXBpL3BiL3BiX2F0dHJiBnBy",
            "b3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::PbGameconfig.GameconfigReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Attr.AddField), global::Attr.AddField.Parser, new[]{ "AttrType", "AttrValue", "AddType", "TargetType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Attr.ModuleAttrAdd), global::Attr.ModuleAttrAdd.Parser, new[]{ "Name", "Source", "Fields" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// AddField 属性加成信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AddField : pb::IMessage<AddField>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AddField> _parser = new pb::MessageParser<AddField>(() => new AddField());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AddField> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Attr.AttrReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AddField() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AddField(AddField other) : this() {
      attrType_ = other.attrType_;
      attrValue_ = other.attrValue_;
      addType_ = other.addType_;
      targetType_ = other.targetType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AddField Clone() {
      return new AddField(this);
    }

    /// <summary>Field number for the "attr_type" field.</summary>
    public const int AttrTypeFieldNumber = 1;
    private global::PbGameconfig.attributes_type attrType_ = global::PbGameconfig.attributes_type.Nil;
    /// <summary>
    /// 属性类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.attributes_type AttrType {
      get { return attrType_; }
      set {
        attrType_ = value;
      }
    }

    /// <summary>Field number for the "attr_value" field.</summary>
    public const int AttrValueFieldNumber = 2;
    private long attrValue_;
    /// <summary>
    /// 属性值, 如果是比例值，则为万分比
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long AttrValue {
      get { return attrValue_; }
      set {
        attrValue_ = value;
      }
    }

    /// <summary>Field number for the "add_type" field.</summary>
    public const int AddTypeFieldNumber = 3;
    private global::PbGameconfig.valuetype addType_ = global::PbGameconfig.valuetype.Nil;
    /// <summary>
    /// 属性添加方式, 1 固定值, 2 万分比
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.valuetype AddType {
      get { return addType_; }
      set {
        addType_ = value;
      }
    }

    /// <summary>Field number for the "target_type" field.</summary>
    public const int TargetTypeFieldNumber = 4;
    private global::PbGameconfig.attributes_target targetType_ = global::PbGameconfig.attributes_target.Nil;
    /// <summary>
    /// 生效目标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.attributes_target TargetType {
      get { return targetType_; }
      set {
        targetType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AddField);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AddField other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AttrType != other.AttrType) return false;
      if (AttrValue != other.AttrValue) return false;
      if (AddType != other.AddType) return false;
      if (TargetType != other.TargetType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (AttrType != global::PbGameconfig.attributes_type.Nil) hash ^= AttrType.GetHashCode();
      if (AttrValue != 0L) hash ^= AttrValue.GetHashCode();
      if (AddType != global::PbGameconfig.valuetype.Nil) hash ^= AddType.GetHashCode();
      if (TargetType != global::PbGameconfig.attributes_target.Nil) hash ^= TargetType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (AttrType != global::PbGameconfig.attributes_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) AttrType);
      }
      if (AttrValue != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(AttrValue);
      }
      if (AddType != global::PbGameconfig.valuetype.Nil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) AddType);
      }
      if (TargetType != global::PbGameconfig.attributes_target.Nil) {
        output.WriteRawTag(32);
        output.WriteEnum((int) TargetType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (AttrType != global::PbGameconfig.attributes_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) AttrType);
      }
      if (AttrValue != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(AttrValue);
      }
      if (AddType != global::PbGameconfig.valuetype.Nil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) AddType);
      }
      if (TargetType != global::PbGameconfig.attributes_target.Nil) {
        output.WriteRawTag(32);
        output.WriteEnum((int) TargetType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (AttrType != global::PbGameconfig.attributes_type.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) AttrType);
      }
      if (AttrValue != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(AttrValue);
      }
      if (AddType != global::PbGameconfig.valuetype.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) AddType);
      }
      if (TargetType != global::PbGameconfig.attributes_target.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TargetType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AddField other) {
      if (other == null) {
        return;
      }
      if (other.AttrType != global::PbGameconfig.attributes_type.Nil) {
        AttrType = other.AttrType;
      }
      if (other.AttrValue != 0L) {
        AttrValue = other.AttrValue;
      }
      if (other.AddType != global::PbGameconfig.valuetype.Nil) {
        AddType = other.AddType;
      }
      if (other.TargetType != global::PbGameconfig.attributes_target.Nil) {
        TargetType = other.TargetType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AttrType = (global::PbGameconfig.attributes_type) input.ReadEnum();
            break;
          }
          case 16: {
            AttrValue = input.ReadInt64();
            break;
          }
          case 24: {
            AddType = (global::PbGameconfig.valuetype) input.ReadEnum();
            break;
          }
          case 32: {
            TargetType = (global::PbGameconfig.attributes_target) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            AttrType = (global::PbGameconfig.attributes_type) input.ReadEnum();
            break;
          }
          case 16: {
            AttrValue = input.ReadInt64();
            break;
          }
          case 24: {
            AddType = (global::PbGameconfig.valuetype) input.ReadEnum();
            break;
          }
          case 32: {
            TargetType = (global::PbGameconfig.attributes_target) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// ModuleAttrAdd 一个模块的属性加成
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ModuleAttrAdd : pb::IMessage<ModuleAttrAdd>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ModuleAttrAdd> _parser = new pb::MessageParser<ModuleAttrAdd>(() => new ModuleAttrAdd());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ModuleAttrAdd> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Attr.AttrReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ModuleAttrAdd() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ModuleAttrAdd(ModuleAttrAdd other) : this() {
      name_ = other.name_;
      source_ = other.source_;
      fields_ = other.fields_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ModuleAttrAdd Clone() {
      return new ModuleAttrAdd(this);
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 1;
    private string name_ = "";
    /// <summary>
    /// 模块名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "source" field.</summary>
    public const int SourceFieldNumber = 2;
    private global::PbGameconfig.attributessource source_ = global::PbGameconfig.attributessource.Nil;
    /// <summary>
    /// 属性来源
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.attributessource Source {
      get { return source_; }
      set {
        source_ = value;
      }
    }

    /// <summary>Field number for the "fields" field.</summary>
    public const int FieldsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Attr.AddField> _repeated_fields_codec
        = pb::FieldCodec.ForMessage(26, global::Attr.AddField.Parser);
    private readonly pbc::RepeatedField<global::Attr.AddField> fields_ = new pbc::RepeatedField<global::Attr.AddField>();
    /// <summary>
    /// 属性加成列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Attr.AddField> Fields {
      get { return fields_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ModuleAttrAdd);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ModuleAttrAdd other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Name != other.Name) return false;
      if (Source != other.Source) return false;
      if(!fields_.Equals(other.fields_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Source != global::PbGameconfig.attributessource.Nil) hash ^= Source.GetHashCode();
      hash ^= fields_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (Source != global::PbGameconfig.attributessource.Nil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Source);
      }
      fields_.WriteTo(output, _repeated_fields_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (Source != global::PbGameconfig.attributessource.Nil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Source);
      }
      fields_.WriteTo(ref output, _repeated_fields_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Source != global::PbGameconfig.attributessource.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Source);
      }
      size += fields_.CalculateSize(_repeated_fields_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ModuleAttrAdd other) {
      if (other == null) {
        return;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Source != global::PbGameconfig.attributessource.Nil) {
        Source = other.Source;
      }
      fields_.Add(other.fields_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 16: {
            Source = (global::PbGameconfig.attributessource) input.ReadEnum();
            break;
          }
          case 26: {
            fields_.AddEntriesFrom(input, _repeated_fields_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 16: {
            Source = (global::PbGameconfig.attributessource) input.ReadEnum();
            break;
          }
          case 26: {
            fields_.AddEntriesFrom(ref input, _repeated_fields_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
