// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: rank.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Rank {

  /// <summary>Holder for reflection information generated from rank.proto</summary>
  public static partial class RankReflection {

    #region Descriptor
    /// <summary>File descriptor for rank.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static RankReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CgpyYW5rLnByb3RvEgRyYW5rIkMKBFJhbmsSDQoFaW5kZXgYASABKAUSCgoC",
            "aWQYAiABKAQSEQoJc2VydmVyX2lkGAMgASgNEg0KBXNjb3JlGAQgASgDImoK",
            "C1JhbmtMaXN0UmVxEhwKBHR5cGUYASABKA4yDi5yYW5rLlJhbmtUeXBlEgwK",
            "BGZyb20YAiABKAUSCgoCdG8YAyABKAUSIwoIc3ViX3R5cGUYBCABKA4yES5y",
            "YW5rLlJhbmtTdWJUeXBlIogBCgxSYW5rTGlzdFJlc3ASHAoEdHlwZRgBIAEo",
            "DjIOLnJhbmsuUmFua1R5cGUSGQoFcmFua3MYAiADKAsyCi5yYW5rLlJhbmsS",
            "GgoGbXlSYW5rGAMgASgLMgoucmFuay5SYW5rEiMKCHN1Yl90eXBlGAQgASgO",
            "MhEucmFuay5SYW5rU3ViVHlwZSpoCghSYW5rVHlwZRISCg5VbmlvbkZpZ2h0",
            "UmFuaxAAEhEKDVVuaW9uS2lsbFJhbmsQARIaChZVbmlvbkRvbmF0aW9uRGFp",
            "bHlSYW5rEAISGQoVVW5pb25Eb25hdGlvbldlZWtSYW5rEAMqMAoLUmFua1N1",
            "YlR5cGUSCgoGTW9kdWxlEAASCgoGU2VydmVyEAESCQoFV29ybGQQAkIXWhVz",
            "ZXJ2ZXIvYXBpL3BiL3BiX3JhbmtiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Rank.RankType), typeof(global::Rank.RankSubType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Rank.Rank), global::Rank.Rank.Parser, new[]{ "Index", "Id", "ServerId", "Score" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Rank.RankListReq), global::Rank.RankListReq.Parser, new[]{ "Type", "From", "To", "SubType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Rank.RankListResp), global::Rank.RankListResp.Parser, new[]{ "Type", "Ranks", "MyRank", "SubType" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum RankType {
    /// <summary>
    /// 联盟 0~100
    /// </summary>
    [pbr::OriginalName("UnionFightRank")] UnionFightRank = 0,
    /// <summary>
    /// 联盟击杀排行榜
    /// </summary>
    [pbr::OriginalName("UnionKillRank")] UnionKillRank = 1,
    /// <summary>
    /// 联盟每日捐献排行榜
    /// </summary>
    [pbr::OriginalName("UnionDonationDailyRank")] UnionDonationDailyRank = 2,
    /// <summary>
    /// 联盟每周捐献排行榜
    /// </summary>
    [pbr::OriginalName("UnionDonationWeekRank")] UnionDonationWeekRank = 3,
  }

  public enum RankSubType {
    /// <summary>
    /// 功能内的榜
    /// </summary>
    [pbr::OriginalName("Module")] Module = 0,
    /// <summary>
    /// 本服榜
    /// </summary>
    [pbr::OriginalName("Server")] Server = 1,
    /// <summary>
    /// 全服榜
    /// </summary>
    [pbr::OriginalName("World")] World = 2,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Rank : pb::IMessage<Rank>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Rank> _parser = new pb::MessageParser<Rank>(() => new Rank());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Rank> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Rank.RankReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Rank() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Rank(Rank other) : this() {
      index_ = other.index_;
      id_ = other.id_;
      serverId_ = other.serverId_;
      score_ = other.score_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Rank Clone() {
      return new Rank(this);
    }

    /// <summary>Field number for the "index" field.</summary>
    public const int IndexFieldNumber = 1;
    private int index_;
    /// <summary>
    /// 排行
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Index {
      get { return index_; }
      set {
        index_ = value;
      }
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 2;
    private ulong id_;
    /// <summary>
    /// 角色id或联盟id等，根据排行榜类型区分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 3;
    private uint serverId_;
    /// <summary>
    /// 服务器id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int ScoreFieldNumber = 4;
    private long score_;
    /// <summary>
    /// 分数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Rank);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Rank other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Index != other.Index) return false;
      if (Id != other.Id) return false;
      if (ServerId != other.ServerId) return false;
      if (Score != other.Score) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Index != 0) hash ^= Index.GetHashCode();
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (Score != 0L) hash ^= Score.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Index);
      }
      if (Id != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(Id);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (Score != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Score);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Index != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Index);
      }
      if (Id != 0UL) {
        output.WriteRawTag(16);
        output.WriteUInt64(Id);
      }
      if (ServerId != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ServerId);
      }
      if (Score != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Score);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Index);
      }
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (Score != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Score);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Rank other) {
      if (other == null) {
        return;
      }
      if (other.Index != 0) {
        Index = other.Index;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.Score != 0L) {
        Score = other.Score;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Index = input.ReadInt32();
            break;
          }
          case 16: {
            Id = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 32: {
            Score = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Index = input.ReadInt32();
            break;
          }
          case 16: {
            Id = input.ReadUInt64();
            break;
          }
          case 24: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 32: {
            Score = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 排行榜列表
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RankListReq : pb::IMessage<RankListReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RankListReq> _parser = new pb::MessageParser<RankListReq>(() => new RankListReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RankListReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Rank.RankReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RankListReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RankListReq(RankListReq other) : this() {
      type_ = other.type_;
      from_ = other.from_;
      to_ = other.to_;
      subType_ = other.subType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RankListReq Clone() {
      return new RankListReq(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::Rank.RankType type_ = global::Rank.RankType.UnionFightRank;
    /// <summary>
    /// 排行榜类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Rank.RankType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "from" field.</summary>
    public const int FromFieldNumber = 2;
    private int from_;
    /// <summary>
    /// 从第几条开始，0为从第一条开始
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int From {
      get { return from_; }
      set {
        from_ = value;
      }
    }

    /// <summary>Field number for the "to" field.</summary>
    public const int ToFieldNumber = 3;
    private int to_;
    /// <summary>
    /// 结束第几条，-1为获取全部
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int To {
      get { return to_; }
      set {
        to_ = value;
      }
    }

    /// <summary>Field number for the "sub_type" field.</summary>
    public const int SubTypeFieldNumber = 4;
    private global::Rank.RankSubType subType_ = global::Rank.RankSubType.Module;
    /// <summary>
    /// 子类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Rank.RankSubType SubType {
      get { return subType_; }
      set {
        subType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RankListReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RankListReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (From != other.From) return false;
      if (To != other.To) return false;
      if (SubType != other.SubType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::Rank.RankType.UnionFightRank) hash ^= Type.GetHashCode();
      if (From != 0) hash ^= From.GetHashCode();
      if (To != 0) hash ^= To.GetHashCode();
      if (SubType != global::Rank.RankSubType.Module) hash ^= SubType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::Rank.RankType.UnionFightRank) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (From != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(From);
      }
      if (To != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(To);
      }
      if (SubType != global::Rank.RankSubType.Module) {
        output.WriteRawTag(32);
        output.WriteEnum((int) SubType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::Rank.RankType.UnionFightRank) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (From != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(From);
      }
      if (To != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(To);
      }
      if (SubType != global::Rank.RankSubType.Module) {
        output.WriteRawTag(32);
        output.WriteEnum((int) SubType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::Rank.RankType.UnionFightRank) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (From != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(From);
      }
      if (To != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(To);
      }
      if (SubType != global::Rank.RankSubType.Module) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) SubType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RankListReq other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::Rank.RankType.UnionFightRank) {
        Type = other.Type;
      }
      if (other.From != 0) {
        From = other.From;
      }
      if (other.To != 0) {
        To = other.To;
      }
      if (other.SubType != global::Rank.RankSubType.Module) {
        SubType = other.SubType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::Rank.RankType) input.ReadEnum();
            break;
          }
          case 16: {
            From = input.ReadInt32();
            break;
          }
          case 24: {
            To = input.ReadInt32();
            break;
          }
          case 32: {
            SubType = (global::Rank.RankSubType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::Rank.RankType) input.ReadEnum();
            break;
          }
          case 16: {
            From = input.ReadInt32();
            break;
          }
          case 24: {
            To = input.ReadInt32();
            break;
          }
          case 32: {
            SubType = (global::Rank.RankSubType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RankListResp : pb::IMessage<RankListResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RankListResp> _parser = new pb::MessageParser<RankListResp>(() => new RankListResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RankListResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Rank.RankReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RankListResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RankListResp(RankListResp other) : this() {
      type_ = other.type_;
      ranks_ = other.ranks_.Clone();
      myRank_ = other.myRank_ != null ? other.myRank_.Clone() : null;
      subType_ = other.subType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RankListResp Clone() {
      return new RankListResp(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::Rank.RankType type_ = global::Rank.RankType.UnionFightRank;
    /// <summary>
    /// 排行榜类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Rank.RankType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "ranks" field.</summary>
    public const int RanksFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Rank.Rank> _repeated_ranks_codec
        = pb::FieldCodec.ForMessage(18, global::Rank.Rank.Parser);
    private readonly pbc::RepeatedField<global::Rank.Rank> ranks_ = new pbc::RepeatedField<global::Rank.Rank>();
    /// <summary>
    /// 排行榜列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Rank.Rank> Ranks {
      get { return ranks_; }
    }

    /// <summary>Field number for the "myRank" field.</summary>
    public const int MyRankFieldNumber = 3;
    private global::Rank.Rank myRank_;
    /// <summary>
    /// 该类型下我的排名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Rank.Rank MyRank {
      get { return myRank_; }
      set {
        myRank_ = value;
      }
    }

    /// <summary>Field number for the "sub_type" field.</summary>
    public const int SubTypeFieldNumber = 4;
    private global::Rank.RankSubType subType_ = global::Rank.RankSubType.Module;
    /// <summary>
    /// 子类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Rank.RankSubType SubType {
      get { return subType_; }
      set {
        subType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RankListResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RankListResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if(!ranks_.Equals(other.ranks_)) return false;
      if (!object.Equals(MyRank, other.MyRank)) return false;
      if (SubType != other.SubType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::Rank.RankType.UnionFightRank) hash ^= Type.GetHashCode();
      hash ^= ranks_.GetHashCode();
      if (myRank_ != null) hash ^= MyRank.GetHashCode();
      if (SubType != global::Rank.RankSubType.Module) hash ^= SubType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::Rank.RankType.UnionFightRank) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      ranks_.WriteTo(output, _repeated_ranks_codec);
      if (myRank_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(MyRank);
      }
      if (SubType != global::Rank.RankSubType.Module) {
        output.WriteRawTag(32);
        output.WriteEnum((int) SubType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::Rank.RankType.UnionFightRank) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      ranks_.WriteTo(ref output, _repeated_ranks_codec);
      if (myRank_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(MyRank);
      }
      if (SubType != global::Rank.RankSubType.Module) {
        output.WriteRawTag(32);
        output.WriteEnum((int) SubType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::Rank.RankType.UnionFightRank) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      size += ranks_.CalculateSize(_repeated_ranks_codec);
      if (myRank_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MyRank);
      }
      if (SubType != global::Rank.RankSubType.Module) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) SubType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RankListResp other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::Rank.RankType.UnionFightRank) {
        Type = other.Type;
      }
      ranks_.Add(other.ranks_);
      if (other.myRank_ != null) {
        if (myRank_ == null) {
          MyRank = new global::Rank.Rank();
        }
        MyRank.MergeFrom(other.MyRank);
      }
      if (other.SubType != global::Rank.RankSubType.Module) {
        SubType = other.SubType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::Rank.RankType) input.ReadEnum();
            break;
          }
          case 18: {
            ranks_.AddEntriesFrom(input, _repeated_ranks_codec);
            break;
          }
          case 26: {
            if (myRank_ == null) {
              MyRank = new global::Rank.Rank();
            }
            input.ReadMessage(MyRank);
            break;
          }
          case 32: {
            SubType = (global::Rank.RankSubType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::Rank.RankType) input.ReadEnum();
            break;
          }
          case 18: {
            ranks_.AddEntriesFrom(ref input, _repeated_ranks_codec);
            break;
          }
          case 26: {
            if (myRank_ == null) {
              MyRank = new global::Rank.Rank();
            }
            input.ReadMessage(MyRank);
            break;
          }
          case 32: {
            SubType = (global::Rank.RankSubType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
