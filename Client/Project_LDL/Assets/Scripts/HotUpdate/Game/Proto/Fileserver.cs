// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: fileserver.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Fileserver {

  /// <summary>Holder for reflection information generated from fileserver.proto</summary>
  public static partial class FileserverReflection {

    #region Descriptor
    /// <summary>File descriptor for fileserver.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static FileserverReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBmaWxlc2VydmVyLnByb3RvEgpmaWxlc2VydmVyImgKE0ZpbGVTZXJ2ZXJV",
            "cGxvYWRSZXESJwoJZmlsZV90eXBlGAEgASgOMhQuZmlsZXNlcnZlci5GaWxl",
            "VHlwZRIRCglmaWxlX2RhdGEYAiABKAwSFQoNZmlsZV9leHRfbmFtZRgDIAEo",
            "CSIpChRGaWxlU2VydmVyVXBsb2FkUmVzcBIRCglmaWxlX25hbWUYASABKAki",
            "hAEKGlB1c2hGaWxlU2VydmVyVXBsb2FkUmVzdWx0EicKCWZpbGVfdHlwZRgB",
            "IAEoDjIULmZpbGVzZXJ2ZXIuRmlsZVR5cGUSEQoJZmlsZV9uYW1lGAIgASgJ",
            "EioKBnN0YXR1cxgDIAEoDjIaLmZpbGVzZXJ2ZXIuRmlsZVN0YXR1c1R5cGUq",
            "NgoIRmlsZVR5cGUSCAoETm9uZRAAEg8KC0hlYWRQaWN0dXJlEAESDwoLQ2hh",
            "dFBpY3R1cmUQAiomCg5GaWxlU3RhdHVzVHlwZRIICgRQYXNzEAASCgoGUmVq",
            "ZWN0EAFCHVobc2VydmVyL2FwaS9wYi9wYl9maWxlc2VydmVyYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Fileserver.FileType), typeof(global::Fileserver.FileStatusType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Fileserver.FileServerUploadReq), global::Fileserver.FileServerUploadReq.Parser, new[]{ "FileType", "FileData", "FileExtName" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fileserver.FileServerUploadResp), global::Fileserver.FileServerUploadResp.Parser, new[]{ "FileName" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Fileserver.PushFileServerUploadResult), global::Fileserver.PushFileServerUploadResult.Parser, new[]{ "FileType", "FileName", "Status" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum FileType {
    [pbr::OriginalName("None")] None = 0,
    /// <summary>
    /// 头像
    /// </summary>
    [pbr::OriginalName("HeadPicture")] HeadPicture = 1,
    /// <summary>
    /// 聊天图片
    /// </summary>
    [pbr::OriginalName("ChatPicture")] ChatPicture = 2,
  }

  public enum FileStatusType {
    /// <summary>
    ///  通过
    /// </summary>
    [pbr::OriginalName("Pass")] Pass = 0,
    /// <summary>
    /// 拒绝
    /// </summary>
    [pbr::OriginalName("Reject")] Reject = 1,
  }

  #endregion

  #region Messages
  /// <summary>
  /// 文件上传
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FileServerUploadReq : pb::IMessage<FileServerUploadReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FileServerUploadReq> _parser = new pb::MessageParser<FileServerUploadReq>(() => new FileServerUploadReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FileServerUploadReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fileserver.FileserverReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FileServerUploadReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FileServerUploadReq(FileServerUploadReq other) : this() {
      fileType_ = other.fileType_;
      fileData_ = other.fileData_;
      fileExtName_ = other.fileExtName_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FileServerUploadReq Clone() {
      return new FileServerUploadReq(this);
    }

    /// <summary>Field number for the "file_type" field.</summary>
    public const int FileTypeFieldNumber = 1;
    private global::Fileserver.FileType fileType_ = global::Fileserver.FileType.None;
    /// <summary>
    /// 文件类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fileserver.FileType FileType {
      get { return fileType_; }
      set {
        fileType_ = value;
      }
    }

    /// <summary>Field number for the "file_data" field.</summary>
    public const int FileDataFieldNumber = 2;
    private pb::ByteString fileData_ = pb::ByteString.Empty;
    /// <summary>
    /// 文件数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString FileData {
      get { return fileData_; }
      set {
        fileData_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "file_ext_name" field.</summary>
    public const int FileExtNameFieldNumber = 3;
    private string fileExtName_ = "";
    /// <summary>
    /// 文件扩展名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string FileExtName {
      get { return fileExtName_; }
      set {
        fileExtName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FileServerUploadReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FileServerUploadReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (FileType != other.FileType) return false;
      if (FileData != other.FileData) return false;
      if (FileExtName != other.FileExtName) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (FileType != global::Fileserver.FileType.None) hash ^= FileType.GetHashCode();
      if (FileData.Length != 0) hash ^= FileData.GetHashCode();
      if (FileExtName.Length != 0) hash ^= FileExtName.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (FileType != global::Fileserver.FileType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) FileType);
      }
      if (FileData.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(FileData);
      }
      if (FileExtName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(FileExtName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (FileType != global::Fileserver.FileType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) FileType);
      }
      if (FileData.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(FileData);
      }
      if (FileExtName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(FileExtName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (FileType != global::Fileserver.FileType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) FileType);
      }
      if (FileData.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(FileData);
      }
      if (FileExtName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FileExtName);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FileServerUploadReq other) {
      if (other == null) {
        return;
      }
      if (other.FileType != global::Fileserver.FileType.None) {
        FileType = other.FileType;
      }
      if (other.FileData.Length != 0) {
        FileData = other.FileData;
      }
      if (other.FileExtName.Length != 0) {
        FileExtName = other.FileExtName;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            FileType = (global::Fileserver.FileType) input.ReadEnum();
            break;
          }
          case 18: {
            FileData = input.ReadBytes();
            break;
          }
          case 26: {
            FileExtName = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            FileType = (global::Fileserver.FileType) input.ReadEnum();
            break;
          }
          case 18: {
            FileData = input.ReadBytes();
            break;
          }
          case 26: {
            FileExtName = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FileServerUploadResp : pb::IMessage<FileServerUploadResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FileServerUploadResp> _parser = new pb::MessageParser<FileServerUploadResp>(() => new FileServerUploadResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FileServerUploadResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fileserver.FileserverReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FileServerUploadResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FileServerUploadResp(FileServerUploadResp other) : this() {
      fileName_ = other.fileName_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FileServerUploadResp Clone() {
      return new FileServerUploadResp(this);
    }

    /// <summary>Field number for the "file_name" field.</summary>
    public const int FileNameFieldNumber = 1;
    private string fileName_ = "";
    /// <summary>
    /// 文件名，uuid，这里会立即返回，结果靠PushFileServerUploadResult推送
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string FileName {
      get { return fileName_; }
      set {
        fileName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FileServerUploadResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FileServerUploadResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (FileName != other.FileName) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (FileName.Length != 0) hash ^= FileName.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (FileName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(FileName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (FileName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(FileName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (FileName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FileName);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FileServerUploadResp other) {
      if (other == null) {
        return;
      }
      if (other.FileName.Length != 0) {
        FileName = other.FileName;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            FileName = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            FileName = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 推送文件上传结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushFileServerUploadResult : pb::IMessage<PushFileServerUploadResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushFileServerUploadResult> _parser = new pb::MessageParser<PushFileServerUploadResult>(() => new PushFileServerUploadResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushFileServerUploadResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Fileserver.FileserverReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushFileServerUploadResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushFileServerUploadResult(PushFileServerUploadResult other) : this() {
      fileType_ = other.fileType_;
      fileName_ = other.fileName_;
      status_ = other.status_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushFileServerUploadResult Clone() {
      return new PushFileServerUploadResult(this);
    }

    /// <summary>Field number for the "file_type" field.</summary>
    public const int FileTypeFieldNumber = 1;
    private global::Fileserver.FileType fileType_ = global::Fileserver.FileType.None;
    /// <summary>
    /// 文件类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fileserver.FileType FileType {
      get { return fileType_; }
      set {
        fileType_ = value;
      }
    }

    /// <summary>Field number for the "file_name" field.</summary>
    public const int FileNameFieldNumber = 2;
    private string fileName_ = "";
    /// <summary>
    /// 文件名，uuid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string FileName {
      get { return fileName_; }
      set {
        fileName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 3;
    private global::Fileserver.FileStatusType status_ = global::Fileserver.FileStatusType.Pass;
    /// <summary>
    /// 上传结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Fileserver.FileStatusType Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushFileServerUploadResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushFileServerUploadResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (FileType != other.FileType) return false;
      if (FileName != other.FileName) return false;
      if (Status != other.Status) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (FileType != global::Fileserver.FileType.None) hash ^= FileType.GetHashCode();
      if (FileName.Length != 0) hash ^= FileName.GetHashCode();
      if (Status != global::Fileserver.FileStatusType.Pass) hash ^= Status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (FileType != global::Fileserver.FileType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) FileType);
      }
      if (FileName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(FileName);
      }
      if (Status != global::Fileserver.FileStatusType.Pass) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (FileType != global::Fileserver.FileType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) FileType);
      }
      if (FileName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(FileName);
      }
      if (Status != global::Fileserver.FileStatusType.Pass) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (FileType != global::Fileserver.FileType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) FileType);
      }
      if (FileName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FileName);
      }
      if (Status != global::Fileserver.FileStatusType.Pass) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushFileServerUploadResult other) {
      if (other == null) {
        return;
      }
      if (other.FileType != global::Fileserver.FileType.None) {
        FileType = other.FileType;
      }
      if (other.FileName.Length != 0) {
        FileName = other.FileName;
      }
      if (other.Status != global::Fileserver.FileStatusType.Pass) {
        Status = other.Status;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            FileType = (global::Fileserver.FileType) input.ReadEnum();
            break;
          }
          case 18: {
            FileName = input.ReadString();
            break;
          }
          case 24: {
            Status = (global::Fileserver.FileStatusType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            FileType = (global::Fileserver.FileType) input.ReadEnum();
            break;
          }
          case 18: {
            FileName = input.ReadString();
            break;
          }
          case 24: {
            Status = (global::Fileserver.FileStatusType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
