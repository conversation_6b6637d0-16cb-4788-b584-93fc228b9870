// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: battle.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Battle {

  /// <summary>Holder for reflection information generated from battle.proto</summary>
  public static partial class BattleReflection {

    #region Descriptor
    /// <summary>File descriptor for battle.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static BattleReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CgxiYXR0bGUucHJvdG8SBmJhdHRsZRoKYXR0ci5wcm90bxoMY29tbW9uLnBy",
            "b3RvGhBnYW1lY29uZmlnLnByb3RvGgp0ZWFtLnByb3RvIq4BCg9CYXR0bGVD",
            "cmVhdGVSZXESHgoIYXR0YWNrZXIYASABKAsyDC5iYXR0bGUuVGVhbRIeCghk",
            "ZWZlbmRlchgCIAEoCzIMLmJhdHRsZS5UZWFtEjAKC2JhdHRsZV90eXBlGAMg",
            "ASgOMhsucGJfZ2FtZWNvbmZpZy5iYXR0bGVfdHlwZXMSKQoJcHVyZV9hcmdz",
            "GAQgASgLMhYuYmF0dGxlLkJhdHRsZVB1cmVBcmdzIksKEEJhdHRsZUNyZWF0",
            "ZVJlc3ASEQoJcmVwb3J0X2lkGAEgASgJEiQKBnJlc3VsdBgCIAEoDjIULmJh",
            "dHRsZS5CYXR0bGVSZXN1bHQiKAoTQmF0dGxlUmVjb3JkUmVhZFJlcRIRCgly",
            "ZXBvcnRfaWQYASABKAkiJgoUQmF0dGxlUmVjb3JkUmVhZFJlc3ASDgoGcmVw",
            "b3J0GAEgASgMIi0KGEJhdHRsZVJlY29yZFJlYWRHcm91cFJlcRIRCglyZXBv",
            "cnRfaWQYASABKAkiLAoZQmF0dGxlUmVjb3JkUmVhZEdyb3VwUmVzcBIPCgdy",
            "ZXBvcnRzGAEgAygMIj0KFEJhdHRsZVBhcnNlUmVwb3J0UmVxEhEKCXJlcG9y",
            "dF9pZBgBIAEoCRISCgpwYXJzZV90eXBlGAIgASgFImcKFUJhdHRsZVBhcnNl",
            "UmVwb3J0UmVzcBIeCghhdHRhY2tlchgBIAEoCzIMLmJhdHRsZS5UZWFtEh4K",
            "CGRlZmVuZGVyGAIgASgLMgwuYmF0dGxlLlRlYW0SDgoGcmFuZG9tGAMgAygF",
            "IioKFUJhdHRsZUV4cG9ydFJlcG9ydFJlcRIRCglyZXBvcnRfaWQYASABKAki",
            "KwoWQmF0dGxlRXhwb3J0UmVwb3J0UmVzcBIRCglmaWxlX3BhdGgYASABKAki",
            "fwoEVGVhbRIhCgl0ZWFtX3R5cGUYASABKA4yDi50ZWFtLlRlYW1UeXBlEh4K",
            "BHJvbGUYAiABKAsyEC5iYXR0bGUuVGVhbVJvbGUSIAoGaGVyb2VzGAMgAygL",
            "MhAuYmF0dGxlLlRlYW1IZXJvEhIKCmlzX21vbnN0ZXIYBCABKAgilwIKCFRl",
            "YW1Sb2xlEhEKCXNlcnZlcl9pZBgBIAEoDRITCgtzZXJ2ZXJfbmFtZRgCIAEo",
            "CRIKCgJpZBgDIAEoBBIMCgRuYW1lGAQgASgJEg0KBWxldmVsGAUgASgNEhgK",
            "EGlzX2N1c3RvbV9hdmF0YXIYBiABKAgSGgoSaGVhZF9zeXN0ZW1fYXZhdGFy",
            "GAcgASgNEhoKEmhlYWRfY3VzdG9tX2F2YXRhchgIIAEoCRITCgtoZWFkX2Jv",
            "cmRlchgJIAEoDRINCgVwb3dlchgKIAEoBBITCgtsZWFndWVfbmFtZRgLIAEo",
            "CRIMCgRsYW5nGAwgASgFEg4KBmdlbmRlchgNIAEoBRIRCgl2aXBfbGV2ZWwY",
            "DiABKAUi3AEKCFRlYW1IZXJvEiMKBGNvZGUYASABKA4yFS5wYl9nYW1lY29u",
            "ZmlnLml0ZW1pZBILCgNwb3MYAiABKAUSDQoFbGV2ZWwYAyABKA0SEgoKc3Rh",
            "cl9zdGFnZRgEIAEoDRINCgVwb3dlchgFIAEoBBIlCgZza2lsbHMYBiADKAsy",
            "FS5iYXR0bGUuVGVhbUhlcm9Ta2lsbBIbCgVhdHRycxgHIAMoCzIMLmNvbW1v",
            "bi5BdHRyEigKB3NvaWxkZXIYCCABKAsyFy5iYXR0bGUuVGVhbUhlcm9Tb2xk",
            "aWVyIjwKDVRlYW1IZXJvU2tpbGwSCgoCaWQYASABKAUSEAoIZ3JvdXBfaWQY",
            "AiABKAUSDQoFbGV2ZWwYAyABKAUiLQoPVGVhbUhlcm9Tb2xkaWVyEg0KBWxl",
            "dmVsGAEgASgFEgsKA251bRgCIAEoBSJ1CgtCYXR0bGVTdGF0cxIQCghkdXJh",
            "dGlvbhgBIAEoBRIpCghhdHRhY2tlchgCIAMoCzIXLmJhdHRsZS5CYXR0bGVU",
            "ZWFtU3RhdHMSKQoIZGVmZW5kZXIYAyADKAsyFy5iYXR0bGUuQmF0dGxlVGVh",
            "bVN0YXRzIlwKD0JhdHRsZVRlYW1TdGF0cxILCgNwb3MYASABKAUSDgoGYXR0",
            "YWNrGAIgASgDEg4KBmRlZmVuZBgDIAEoAxIMCgRidWZmGAQgASgDEg4KBmRl",
            "YnVmZhgFIAEoAyL4AQoGUmVwb3J0EhEKCXJlcG9ydF9pZBgBIAEoCRIeCghh",
            "dHRhY2tlchgCIAEoCzIMLmJhdHRsZS5UZWFtEh4KCGRlZmVuZGVyGAMgASgL",
            "MgwuYmF0dGxlLlRlYW0SMAoLYmF0dGxlX3R5cGUYBCABKA4yGy5wYl9nYW1l",
            "Y29uZmlnLmJhdHRsZV90eXBlcxIkCgZyZXN1bHQYBSABKA4yFC5iYXR0bGUu",
            "QmF0dGxlUmVzdWx0Eh8KB2FjdGlvbnMYBiADKAsyDi5iYXR0bGUuQWN0aW9u",
            "EiIKBXN0YXRzGAcgASgLMhMuYmF0dGxlLkJhdHRsZVN0YXRzItYBCg1SZXBv",
            "cnRTdG9yYWdlEhEKCXJlcG9ydF9pZBgBIAEoCRIwCgtiYXR0bGVfdHlwZRgC",
            "IAEoDjIbLnBiX2dhbWVjb25maWcuYmF0dGxlX3R5cGVzEisKDWJhdHRsZV9y",
            "ZXN1bHQYAyABKA4yFC5iYXR0bGUuQmF0dGxlUmVzdWx0Eg8KB3ZlcnNpb24Y",
            "BCABKAUSDgoGcmFuZG9tGAUgAygFEhAKCGF0dGFja2VyGAYgASgEEhAKCGRl",
            "ZmVuZGVyGAcgASgEEg4KBnJlcG9ydBgIIAEoDCKrAQoPQmF0dGxlTG9naWNB",
            "cmdzEhkKEWlzX2F1dG9fZm9ybWF0aW9uGAEgASgIEhsKE2lzX2FsbG93X3Rl",
            "YW1fZW1wdHkYAiABKAgSLwoSYXR0YWNrZXJfYWRkX2F0dHJzGAMgAygLMhMu",
            "YXR0ci5Nb2R1bGVBdHRyQWRkEi8KEmRlZmVuZGVyX2FkZF9hdHRycxgEIAMo",
            "CzITLmF0dHIuTW9kdWxlQXR0ckFkZCJrCg5CYXR0bGVQdXJlQXJncxINCgVl",
            "eHRyYRgBIAEoCRIaChJpc19pZ25vcmVfZGVmZW5kZXIYAiABKAgSHgoWaXNf",
            "aWdub3JlX3N0b3JlX3JlcG9ydBgDIAEoCBIOCgZyYW5kb20YBCADKAUinAEK",
            "BkFjdGlvbhINCgVmcmFtZRgBIAEoBRIOCgZjYXN0ZXIYAiABKAUSDgoGdGFy",
            "Z2V0GAMgAygFEgwKBG1hc2sYBCABKAUSIgoGYWN0aW9uGAUgASgOMhIuYmF0",
            "dGxlLkFjdGlvblR5cGUSIgoGY2hhbmdlGAYgASgOMhIuYmF0dGxlLkNoYW5n",
            "ZVR5cGUSDQoFdmFsdWUYByABKBIqPwoMQmF0dGxlUmVzdWx0Eg0KCVJlc3Vs",
            "dE5pbBAAEg8KC0F0dGFja2VyV2luEAESDwoLRGVmZW5kZXJXaW4QAipeCgpB",
            "Y3Rpb25UeXBlEg0KCUFjdGlvbk5pbBAAEgkKBVNraWxsEAESCAoEQXR0chAC",
            "EggKBEJ1ZmYQAxILCgdTcGVjaWFsEAQSCQoFUGxhbmUQBRIKCgZSZXN1bHQQ",
            "ZCp2CgpDaGFuZ2VUeXBlEg0KCUNoYW5nZU5pbBAAEgoKBkF0dHJIUBABEg0K",
            "CUF0dHJNYXhIUBACEgsKB0J1ZmZBZGQQCxIOCgpCdWZmUmVtb3ZlEAwSBwoD",
            "RGllEBUSDgoKUmVzdXJnZW5jZRAWEggKBE1hc2sQH0IZWhdzZXJ2ZXIvYXBp",
            "L3BiL3BiX2JhdHRsZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Attr.AttrReflection.Descriptor, global::Common.CommonReflection.Descriptor, global::PbGameconfig.GameconfigReflection.Descriptor, global::Team.TeamReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Battle.BattleResult), typeof(global::Battle.ActionType), typeof(global::Battle.ChangeType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleCreateReq), global::Battle.BattleCreateReq.Parser, new[]{ "Attacker", "Defender", "BattleType", "PureArgs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleCreateResp), global::Battle.BattleCreateResp.Parser, new[]{ "ReportId", "Result" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleRecordReadReq), global::Battle.BattleRecordReadReq.Parser, new[]{ "ReportId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleRecordReadResp), global::Battle.BattleRecordReadResp.Parser, new[]{ "Report" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleRecordReadGroupReq), global::Battle.BattleRecordReadGroupReq.Parser, new[]{ "ReportId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleRecordReadGroupResp), global::Battle.BattleRecordReadGroupResp.Parser, new[]{ "Reports" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleParseReportReq), global::Battle.BattleParseReportReq.Parser, new[]{ "ReportId", "ParseType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleParseReportResp), global::Battle.BattleParseReportResp.Parser, new[]{ "Attacker", "Defender", "Random" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleExportReportReq), global::Battle.BattleExportReportReq.Parser, new[]{ "ReportId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleExportReportResp), global::Battle.BattleExportReportResp.Parser, new[]{ "FilePath" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.Team), global::Battle.Team.Parser, new[]{ "TeamType", "Role", "Heroes", "IsMonster" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.TeamRole), global::Battle.TeamRole.Parser, new[]{ "ServerId", "ServerName", "Id", "Name", "Level", "IsCustomAvatar", "HeadSystemAvatar", "HeadCustomAvatar", "HeadBorder", "Power", "LeagueName", "Lang", "Gender", "VipLevel" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.TeamHero), global::Battle.TeamHero.Parser, new[]{ "Code", "Pos", "Level", "StarStage", "Power", "Skills", "Attrs", "Soilder" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.TeamHeroSkill), global::Battle.TeamHeroSkill.Parser, new[]{ "Id", "GroupId", "Level" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.TeamHeroSoldier), global::Battle.TeamHeroSoldier.Parser, new[]{ "Level", "Num" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleStats), global::Battle.BattleStats.Parser, new[]{ "Duration", "Attacker", "Defender" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleTeamStats), global::Battle.BattleTeamStats.Parser, new[]{ "Pos", "Attack", "Defend", "Buff", "Debuff" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.Report), global::Battle.Report.Parser, new[]{ "ReportId", "Attacker", "Defender", "BattleType", "Result", "Actions", "Stats" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.ReportStorage), global::Battle.ReportStorage.Parser, new[]{ "ReportId", "BattleType", "BattleResult", "Version", "Random", "Attacker", "Defender", "Report" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattleLogicArgs), global::Battle.BattleLogicArgs.Parser, new[]{ "IsAutoFormation", "IsAllowTeamEmpty", "AttackerAddAttrs", "DefenderAddAttrs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.BattlePureArgs), global::Battle.BattlePureArgs.Parser, new[]{ "Extra", "IsIgnoreDefender", "IsIgnoreStoreReport", "Random" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Battle.Action), global::Battle.Action.Parser, new[]{ "Frame", "Caster", "Target", "Mask", "Action_", "Change", "Value" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum BattleResult {
    [pbr::OriginalName("ResultNil")] ResultNil = 0,
    /// <summary>
    /// 进攻方胜
    /// </summary>
    [pbr::OriginalName("AttackerWin")] AttackerWin = 1,
    /// <summary>
    /// 防守方胜
    /// </summary>
    [pbr::OriginalName("DefenderWin")] DefenderWin = 2,
  }

  /// <summary>
  /// 动作枚举
  /// </summary>
  public enum ActionType {
    /// <summary>
    /// 无
    /// </summary>
    [pbr::OriginalName("ActionNil")] ActionNil = 0,
    /// <summary>
    /// 释放技能
    /// </summary>
    [pbr::OriginalName("Skill")] Skill = 1,
    /// <summary>
    /// 属性变化
    /// </summary>
    [pbr::OriginalName("Attr")] Attr = 2,
    /// <summary>
    /// buff 变化
    /// </summary>
    [pbr::OriginalName("Buff")] Buff = 3,
    /// <summary>
    /// 特殊处理
    /// </summary>
    [pbr::OriginalName("Special")] Special = 4,
    /// <summary>
    /// 无人机
    /// </summary>
    [pbr::OriginalName("Plane")] Plane = 5,
    /// <summary>
    /// 战斗结束
    /// </summary>
    [pbr::OriginalName("Result")] Result = 100,
  }

  /// <summary>
  /// 改变类型
  /// </summary>
  public enum ChangeType {
    /// <summary>
    /// 无
    /// </summary>
    [pbr::OriginalName("ChangeNil")] ChangeNil = 0,
    /// <summary>
    /// 生命值发生变化
    /// </summary>
    [pbr::OriginalName("AttrHP")] AttrHp = 1,
    /// <summary>
    /// 最大生命值变化
    /// </summary>
    [pbr::OriginalName("AttrMaxHP")] AttrMaxHp = 2,
    /// <summary>
    /// 添加 buff, value := 唯一id * 10_000_000 * 持续时间帧 * 1000 + buffID
    /// </summary>
    [pbr::OriginalName("BuffAdd")] BuffAdd = 11,
    /// <summary>
    /// 移除 buff
    /// </summary>
    [pbr::OriginalName("BuffRemove")] BuffRemove = 12,
    /// <summary>
    /// 死亡
    /// </summary>
    [pbr::OriginalName("Die")] Die = 21,
    /// <summary>
    /// 复活
    /// </summary>
    [pbr::OriginalName("Resurgence")] Resurgence = 22,
    /// <summary>
    /// 读取事件掩码
    /// </summary>
    [pbr::OriginalName("Mask")] Mask = 31,
  }

  #endregion

  #region Messages
  /// <summary>
  /// 战斗创建, 401
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleCreateReq : pb::IMessage<BattleCreateReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleCreateReq> _parser = new pb::MessageParser<BattleCreateReq>(() => new BattleCreateReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleCreateReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateReq(BattleCreateReq other) : this() {
      attacker_ = other.attacker_ != null ? other.attacker_.Clone() : null;
      defender_ = other.defender_ != null ? other.defender_.Clone() : null;
      battleType_ = other.battleType_;
      pureArgs_ = other.pureArgs_ != null ? other.pureArgs_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateReq Clone() {
      return new BattleCreateReq(this);
    }

    /// <summary>Field number for the "attacker" field.</summary>
    public const int AttackerFieldNumber = 1;
    private global::Battle.Team attacker_;
    /// <summary>
    /// 进攻方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.Team Attacker {
      get { return attacker_; }
      set {
        attacker_ = value;
      }
    }

    /// <summary>Field number for the "defender" field.</summary>
    public const int DefenderFieldNumber = 2;
    private global::Battle.Team defender_;
    /// <summary>
    /// 防御方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.Team Defender {
      get { return defender_; }
      set {
        defender_ = value;
      }
    }

    /// <summary>Field number for the "battle_type" field.</summary>
    public const int BattleTypeFieldNumber = 3;
    private global::PbGameconfig.battle_types battleType_ = global::PbGameconfig.battle_types._0;
    /// <summary>
    /// 战斗类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.battle_types BattleType {
      get { return battleType_; }
      set {
        battleType_ = value;
      }
    }

    /// <summary>Field number for the "pure_args" field.</summary>
    public const int PureArgsFieldNumber = 4;
    private global::Battle.BattlePureArgs pureArgs_;
    /// <summary>
    /// 战斗参数: 透传给战斗模块
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattlePureArgs PureArgs {
      get { return pureArgs_; }
      set {
        pureArgs_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleCreateReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleCreateReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Attacker, other.Attacker)) return false;
      if (!object.Equals(Defender, other.Defender)) return false;
      if (BattleType != other.BattleType) return false;
      if (!object.Equals(PureArgs, other.PureArgs)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (attacker_ != null) hash ^= Attacker.GetHashCode();
      if (defender_ != null) hash ^= Defender.GetHashCode();
      if (BattleType != global::PbGameconfig.battle_types._0) hash ^= BattleType.GetHashCode();
      if (pureArgs_ != null) hash ^= PureArgs.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (attacker_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Defender);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(24);
        output.WriteEnum((int) BattleType);
      }
      if (pureArgs_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(PureArgs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (attacker_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Defender);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(24);
        output.WriteEnum((int) BattleType);
      }
      if (pureArgs_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(PureArgs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (attacker_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Attacker);
      }
      if (defender_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Defender);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) BattleType);
      }
      if (pureArgs_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(PureArgs);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleCreateReq other) {
      if (other == null) {
        return;
      }
      if (other.attacker_ != null) {
        if (attacker_ == null) {
          Attacker = new global::Battle.Team();
        }
        Attacker.MergeFrom(other.Attacker);
      }
      if (other.defender_ != null) {
        if (defender_ == null) {
          Defender = new global::Battle.Team();
        }
        Defender.MergeFrom(other.Defender);
      }
      if (other.BattleType != global::PbGameconfig.battle_types._0) {
        BattleType = other.BattleType;
      }
      if (other.pureArgs_ != null) {
        if (pureArgs_ == null) {
          PureArgs = new global::Battle.BattlePureArgs();
        }
        PureArgs.MergeFrom(other.PureArgs);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (attacker_ == null) {
              Attacker = new global::Battle.Team();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 18: {
            if (defender_ == null) {
              Defender = new global::Battle.Team();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 24: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 34: {
            if (pureArgs_ == null) {
              PureArgs = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(PureArgs);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (attacker_ == null) {
              Attacker = new global::Battle.Team();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 18: {
            if (defender_ == null) {
              Defender = new global::Battle.Team();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 24: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 34: {
            if (pureArgs_ == null) {
              PureArgs = new global::Battle.BattlePureArgs();
            }
            input.ReadMessage(PureArgs);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleCreateResp : pb::IMessage<BattleCreateResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleCreateResp> _parser = new pb::MessageParser<BattleCreateResp>(() => new BattleCreateResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleCreateResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateResp(BattleCreateResp other) : this() {
      reportId_ = other.reportId_;
      result_ = other.result_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleCreateResp Clone() {
      return new BattleCreateResp(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id/战报组 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 2;
    private global::Battle.BattleResult result_ = global::Battle.BattleResult.ResultNil;
    /// <summary>
    /// 战斗结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattleResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleCreateResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleCreateResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      if (Result != other.Result) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (Result != global::Battle.BattleResult.ResultNil) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (Result != global::Battle.BattleResult.ResultNil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (Result != global::Battle.BattleResult.ResultNil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (Result != global::Battle.BattleResult.ResultNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleCreateResp other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      if (other.Result != global::Battle.BattleResult.ResultNil) {
        Result = other.Result;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            Result = (global::Battle.BattleResult) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            Result = (global::Battle.BattleResult) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 读取一场战报, 481
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleRecordReadReq : pb::IMessage<BattleRecordReadReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleRecordReadReq> _parser = new pb::MessageParser<BattleRecordReadReq>(() => new BattleRecordReadReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleRecordReadReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadReq(BattleRecordReadReq other) : this() {
      reportId_ = other.reportId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadReq Clone() {
      return new BattleRecordReadReq(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleRecordReadReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleRecordReadReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleRecordReadReq other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleRecordReadResp : pb::IMessage<BattleRecordReadResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleRecordReadResp> _parser = new pb::MessageParser<BattleRecordReadResp>(() => new BattleRecordReadResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleRecordReadResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadResp(BattleRecordReadResp other) : this() {
      report_ = other.report_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadResp Clone() {
      return new BattleRecordReadResp(this);
    }

    /// <summary>Field number for the "report" field.</summary>
    public const int ReportFieldNumber = 1;
    private pb::ByteString report_ = pb::ByteString.Empty;
    /// <summary>
    /// 战报, 见下方 Report
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString Report {
      get { return report_; }
      set {
        report_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleRecordReadResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleRecordReadResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Report != other.Report) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Report.Length != 0) hash ^= Report.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Report.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(Report);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Report.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(Report);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Report.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(Report);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleRecordReadResp other) {
      if (other == null) {
        return;
      }
      if (other.Report.Length != 0) {
        Report = other.Report;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Report = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Report = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 读取战报组, 482
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleRecordReadGroupReq : pb::IMessage<BattleRecordReadGroupReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleRecordReadGroupReq> _parser = new pb::MessageParser<BattleRecordReadGroupReq>(() => new BattleRecordReadGroupReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleRecordReadGroupReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadGroupReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadGroupReq(BattleRecordReadGroupReq other) : this() {
      reportId_ = other.reportId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadGroupReq Clone() {
      return new BattleRecordReadGroupReq(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报组 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleRecordReadGroupReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleRecordReadGroupReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleRecordReadGroupReq other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleRecordReadGroupResp : pb::IMessage<BattleRecordReadGroupResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleRecordReadGroupResp> _parser = new pb::MessageParser<BattleRecordReadGroupResp>(() => new BattleRecordReadGroupResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleRecordReadGroupResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadGroupResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadGroupResp(BattleRecordReadGroupResp other) : this() {
      reports_ = other.reports_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleRecordReadGroupResp Clone() {
      return new BattleRecordReadGroupResp(this);
    }

    /// <summary>Field number for the "reports" field.</summary>
    public const int ReportsFieldNumber = 1;
    private static readonly pb::FieldCodec<pb::ByteString> _repeated_reports_codec
        = pb::FieldCodec.ForBytes(10);
    private readonly pbc::RepeatedField<pb::ByteString> reports_ = new pbc::RepeatedField<pb::ByteString>();
    /// <summary>
    /// 战报列表, 见下方 Report 
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<pb::ByteString> Reports {
      get { return reports_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleRecordReadGroupResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleRecordReadGroupResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!reports_.Equals(other.reports_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= reports_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      reports_.WriteTo(output, _repeated_reports_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      reports_.WriteTo(ref output, _repeated_reports_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += reports_.CalculateSize(_repeated_reports_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleRecordReadGroupResp other) {
      if (other == null) {
        return;
      }
      reports_.Add(other.reports_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            reports_.AddEntriesFrom(input, _repeated_reports_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            reports_.AddEntriesFrom(ref input, _repeated_reports_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 解析战报, 483
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleParseReportReq : pb::IMessage<BattleParseReportReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleParseReportReq> _parser = new pb::MessageParser<BattleParseReportReq>(() => new BattleParseReportReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleParseReportReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleParseReportReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleParseReportReq(BattleParseReportReq other) : this() {
      reportId_ = other.reportId_;
      parseType_ = other.parseType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleParseReportReq Clone() {
      return new BattleParseReportReq(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "parse_type" field.</summary>
    public const int ParseTypeFieldNumber = 2;
    private int parseType_;
    /// <summary>
    /// 解析方式 1 从数据库解析; 2 从文件中解析
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ParseType {
      get { return parseType_; }
      set {
        parseType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleParseReportReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleParseReportReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      if (ParseType != other.ParseType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (ParseType != 0) hash ^= ParseType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (ParseType != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ParseType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (ParseType != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ParseType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (ParseType != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ParseType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleParseReportReq other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      if (other.ParseType != 0) {
        ParseType = other.ParseType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            ParseType = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            ParseType = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleParseReportResp : pb::IMessage<BattleParseReportResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleParseReportResp> _parser = new pb::MessageParser<BattleParseReportResp>(() => new BattleParseReportResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleParseReportResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleParseReportResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleParseReportResp(BattleParseReportResp other) : this() {
      attacker_ = other.attacker_ != null ? other.attacker_.Clone() : null;
      defender_ = other.defender_ != null ? other.defender_.Clone() : null;
      random_ = other.random_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleParseReportResp Clone() {
      return new BattleParseReportResp(this);
    }

    /// <summary>Field number for the "attacker" field.</summary>
    public const int AttackerFieldNumber = 1;
    private global::Battle.Team attacker_;
    /// <summary>
    /// 进攻方队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.Team Attacker {
      get { return attacker_; }
      set {
        attacker_ = value;
      }
    }

    /// <summary>Field number for the "defender" field.</summary>
    public const int DefenderFieldNumber = 2;
    private global::Battle.Team defender_;
    /// <summary>
    /// 防御方队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.Team Defender {
      get { return defender_; }
      set {
        defender_ = value;
      }
    }

    /// <summary>Field number for the "random" field.</summary>
    public const int RandomFieldNumber = 3;
    private static readonly pb::FieldCodec<int> _repeated_random_codec
        = pb::FieldCodec.ForInt32(26);
    private readonly pbc::RepeatedField<int> random_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// 随机数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> Random {
      get { return random_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleParseReportResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleParseReportResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Attacker, other.Attacker)) return false;
      if (!object.Equals(Defender, other.Defender)) return false;
      if(!random_.Equals(other.random_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (attacker_ != null) hash ^= Attacker.GetHashCode();
      if (defender_ != null) hash ^= Defender.GetHashCode();
      hash ^= random_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (attacker_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Defender);
      }
      random_.WriteTo(output, _repeated_random_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (attacker_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Defender);
      }
      random_.WriteTo(ref output, _repeated_random_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (attacker_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Attacker);
      }
      if (defender_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Defender);
      }
      size += random_.CalculateSize(_repeated_random_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleParseReportResp other) {
      if (other == null) {
        return;
      }
      if (other.attacker_ != null) {
        if (attacker_ == null) {
          Attacker = new global::Battle.Team();
        }
        Attacker.MergeFrom(other.Attacker);
      }
      if (other.defender_ != null) {
        if (defender_ == null) {
          Defender = new global::Battle.Team();
        }
        Defender.MergeFrom(other.Defender);
      }
      random_.Add(other.random_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (attacker_ == null) {
              Attacker = new global::Battle.Team();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 18: {
            if (defender_ == null) {
              Defender = new global::Battle.Team();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 26:
          case 24: {
            random_.AddEntriesFrom(input, _repeated_random_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (attacker_ == null) {
              Attacker = new global::Battle.Team();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 18: {
            if (defender_ == null) {
              Defender = new global::Battle.Team();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 26:
          case 24: {
            random_.AddEntriesFrom(ref input, _repeated_random_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 导出战报, 484
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleExportReportReq : pb::IMessage<BattleExportReportReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleExportReportReq> _parser = new pb::MessageParser<BattleExportReportReq>(() => new BattleExportReportReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleExportReportReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleExportReportReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleExportReportReq(BattleExportReportReq other) : this() {
      reportId_ = other.reportId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleExportReportReq Clone() {
      return new BattleExportReportReq(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleExportReportReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleExportReportReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleExportReportReq other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleExportReportResp : pb::IMessage<BattleExportReportResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleExportReportResp> _parser = new pb::MessageParser<BattleExportReportResp>(() => new BattleExportReportResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleExportReportResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleExportReportResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleExportReportResp(BattleExportReportResp other) : this() {
      filePath_ = other.filePath_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleExportReportResp Clone() {
      return new BattleExportReportResp(this);
    }

    /// <summary>Field number for the "file_path" field.</summary>
    public const int FilePathFieldNumber = 1;
    private string filePath_ = "";
    /// <summary>
    /// 导出的文件路径
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string FilePath {
      get { return filePath_; }
      set {
        filePath_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleExportReportResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleExportReportResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (FilePath != other.FilePath) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (FilePath.Length != 0) hash ^= FilePath.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (FilePath.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(FilePath);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (FilePath.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(FilePath);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (FilePath.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FilePath);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleExportReportResp other) {
      if (other == null) {
        return;
      }
      if (other.FilePath.Length != 0) {
        FilePath = other.FilePath;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            FilePath = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            FilePath = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 队伍信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Team : pb::IMessage<Team>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Team> _parser = new pb::MessageParser<Team>(() => new Team());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Team> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Team() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Team(Team other) : this() {
      teamType_ = other.teamType_;
      role_ = other.role_ != null ? other.role_.Clone() : null;
      heroes_ = other.heroes_.Clone();
      isMonster_ = other.isMonster_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Team Clone() {
      return new Team(this);
    }

    /// <summary>Field number for the "team_type" field.</summary>
    public const int TeamTypeFieldNumber = 1;
    private global::Team.TeamType teamType_ = global::Team.TeamType.TeamNil;
    /// <summary>
    /// 队伍类型 TeamType
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Team.TeamType TeamType {
      get { return teamType_; }
      set {
        teamType_ = value;
      }
    }

    /// <summary>Field number for the "role" field.</summary>
    public const int RoleFieldNumber = 2;
    private global::Battle.TeamRole role_;
    /// <summary>
    /// 角色列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.TeamRole Role {
      get { return role_; }
      set {
        role_ = value;
      }
    }

    /// <summary>Field number for the "heroes" field.</summary>
    public const int HeroesFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Battle.TeamHero> _repeated_heroes_codec
        = pb::FieldCodec.ForMessage(26, global::Battle.TeamHero.Parser);
    private readonly pbc::RepeatedField<global::Battle.TeamHero> heroes_ = new pbc::RepeatedField<global::Battle.TeamHero>();
    /// <summary>
    /// 英雄信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Battle.TeamHero> Heroes {
      get { return heroes_; }
    }

    /// <summary>Field number for the "is_monster" field.</summary>
    public const int IsMonsterFieldNumber = 4;
    private bool isMonster_;
    /// <summary>
    /// 是否为怪物
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsMonster {
      get { return isMonster_; }
      set {
        isMonster_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Team);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Team other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TeamType != other.TeamType) return false;
      if (!object.Equals(Role, other.Role)) return false;
      if(!heroes_.Equals(other.heroes_)) return false;
      if (IsMonster != other.IsMonster) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (TeamType != global::Team.TeamType.TeamNil) hash ^= TeamType.GetHashCode();
      if (role_ != null) hash ^= Role.GetHashCode();
      hash ^= heroes_.GetHashCode();
      if (IsMonster != false) hash ^= IsMonster.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      if (role_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Role);
      }
      heroes_.WriteTo(output, _repeated_heroes_codec);
      if (IsMonster != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsMonster);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (TeamType != global::Team.TeamType.TeamNil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) TeamType);
      }
      if (role_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Role);
      }
      heroes_.WriteTo(ref output, _repeated_heroes_codec);
      if (IsMonster != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsMonster);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (TeamType != global::Team.TeamType.TeamNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TeamType);
      }
      if (role_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Role);
      }
      size += heroes_.CalculateSize(_repeated_heroes_codec);
      if (IsMonster != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Team other) {
      if (other == null) {
        return;
      }
      if (other.TeamType != global::Team.TeamType.TeamNil) {
        TeamType = other.TeamType;
      }
      if (other.role_ != null) {
        if (role_ == null) {
          Role = new global::Battle.TeamRole();
        }
        Role.MergeFrom(other.Role);
      }
      heroes_.Add(other.heroes_);
      if (other.IsMonster != false) {
        IsMonster = other.IsMonster;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 18: {
            if (role_ == null) {
              Role = new global::Battle.TeamRole();
            }
            input.ReadMessage(Role);
            break;
          }
          case 26: {
            heroes_.AddEntriesFrom(input, _repeated_heroes_codec);
            break;
          }
          case 32: {
            IsMonster = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            TeamType = (global::Team.TeamType) input.ReadEnum();
            break;
          }
          case 18: {
            if (role_ == null) {
              Role = new global::Battle.TeamRole();
            }
            input.ReadMessage(Role);
            break;
          }
          case 26: {
            heroes_.AddEntriesFrom(ref input, _repeated_heroes_codec);
            break;
          }
          case 32: {
            IsMonster = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 队伍中角色信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamRole : pb::IMessage<TeamRole>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamRole> _parser = new pb::MessageParser<TeamRole>(() => new TeamRole());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamRole> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamRole() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamRole(TeamRole other) : this() {
      serverId_ = other.serverId_;
      serverName_ = other.serverName_;
      id_ = other.id_;
      name_ = other.name_;
      level_ = other.level_;
      isCustomAvatar_ = other.isCustomAvatar_;
      headSystemAvatar_ = other.headSystemAvatar_;
      headCustomAvatar_ = other.headCustomAvatar_;
      headBorder_ = other.headBorder_;
      power_ = other.power_;
      leagueName_ = other.leagueName_;
      lang_ = other.lang_;
      gender_ = other.gender_;
      vipLevel_ = other.vipLevel_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamRole Clone() {
      return new TeamRole(this);
    }

    /// <summary>Field number for the "server_id" field.</summary>
    public const int ServerIdFieldNumber = 1;
    private uint serverId_;
    /// <summary>
    /// 所在区服 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerId {
      get { return serverId_; }
      set {
        serverId_ = value;
      }
    }

    /// <summary>Field number for the "server_name" field.</summary>
    public const int ServerNameFieldNumber = 2;
    private string serverName_ = "";
    /// <summary>
    /// 所在区服名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ServerName {
      get { return serverName_; }
      set {
        serverName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 3;
    private ulong id_;
    /// <summary>
    /// 角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 4;
    private string name_ = "";
    /// <summary>
    /// 角色名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 5;
    private uint level_;
    /// <summary>
    /// 角色等级(总部等级)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "is_custom_avatar" field.</summary>
    public const int IsCustomAvatarFieldNumber = 6;
    private bool isCustomAvatar_;
    /// <summary>
    /// 是否是自定义头像
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsCustomAvatar {
      get { return isCustomAvatar_; }
      set {
        isCustomAvatar_ = value;
      }
    }

    /// <summary>Field number for the "head_system_avatar" field.</summary>
    public const int HeadSystemAvatarFieldNumber = 7;
    private uint headSystemAvatar_;
    /// <summary>
    /// 角色头像图标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint HeadSystemAvatar {
      get { return headSystemAvatar_; }
      set {
        headSystemAvatar_ = value;
      }
    }

    /// <summary>Field number for the "head_custom_avatar" field.</summary>
    public const int HeadCustomAvatarFieldNumber = 8;
    private string headCustomAvatar_ = "";
    /// <summary>
    /// 角色自定义头像链接地址
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string HeadCustomAvatar {
      get { return headCustomAvatar_; }
      set {
        headCustomAvatar_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "head_border" field.</summary>
    public const int HeadBorderFieldNumber = 9;
    private uint headBorder_;
    /// <summary>
    /// 角色头像框
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint HeadBorder {
      get { return headBorder_; }
      set {
        headBorder_ = value;
      }
    }

    /// <summary>Field number for the "power" field.</summary>
    public const int PowerFieldNumber = 10;
    private ulong power_;
    /// <summary>
    /// 总战力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Power {
      get { return power_; }
      set {
        power_ = value;
      }
    }

    /// <summary>Field number for the "league_name" field.</summary>
    public const int LeagueNameFieldNumber = 11;
    private string leagueName_ = "";
    /// <summary>
    /// 联盟名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string LeagueName {
      get { return leagueName_; }
      set {
        leagueName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "lang" field.</summary>
    public const int LangFieldNumber = 12;
    private int lang_;
    /// <summary>
    /// 玩家当前语言 language_type.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Lang {
      get { return lang_; }
      set {
        lang_ = value;
      }
    }

    /// <summary>Field number for the "gender" field.</summary>
    public const int GenderFieldNumber = 13;
    private int gender_;
    /// <summary>
    /// 性别，0 未知，1 男，2 女
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Gender {
      get { return gender_; }
      set {
        gender_ = value;
      }
    }

    /// <summary>Field number for the "vip_level" field.</summary>
    public const int VipLevelFieldNumber = 14;
    private int vipLevel_;
    /// <summary>
    /// vip等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int VipLevel {
      get { return vipLevel_; }
      set {
        vipLevel_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamRole);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamRole other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ServerId != other.ServerId) return false;
      if (ServerName != other.ServerName) return false;
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (Level != other.Level) return false;
      if (IsCustomAvatar != other.IsCustomAvatar) return false;
      if (HeadSystemAvatar != other.HeadSystemAvatar) return false;
      if (HeadCustomAvatar != other.HeadCustomAvatar) return false;
      if (HeadBorder != other.HeadBorder) return false;
      if (Power != other.Power) return false;
      if (LeagueName != other.LeagueName) return false;
      if (Lang != other.Lang) return false;
      if (Gender != other.Gender) return false;
      if (VipLevel != other.VipLevel) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ServerId != 0) hash ^= ServerId.GetHashCode();
      if (ServerName.Length != 0) hash ^= ServerName.GetHashCode();
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (IsCustomAvatar != false) hash ^= IsCustomAvatar.GetHashCode();
      if (HeadSystemAvatar != 0) hash ^= HeadSystemAvatar.GetHashCode();
      if (HeadCustomAvatar.Length != 0) hash ^= HeadCustomAvatar.GetHashCode();
      if (HeadBorder != 0) hash ^= HeadBorder.GetHashCode();
      if (Power != 0UL) hash ^= Power.GetHashCode();
      if (LeagueName.Length != 0) hash ^= LeagueName.GetHashCode();
      if (Lang != 0) hash ^= Lang.GetHashCode();
      if (Gender != 0) hash ^= Gender.GetHashCode();
      if (VipLevel != 0) hash ^= VipLevel.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (ServerName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ServerName);
      }
      if (Id != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Name);
      }
      if (Level != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(Level);
      }
      if (IsCustomAvatar != false) {
        output.WriteRawTag(48);
        output.WriteBool(IsCustomAvatar);
      }
      if (HeadSystemAvatar != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(HeadBorder);
      }
      if (Power != 0UL) {
        output.WriteRawTag(80);
        output.WriteUInt64(Power);
      }
      if (LeagueName.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(LeagueName);
      }
      if (Lang != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(Lang);
      }
      if (Gender != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(Gender);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(VipLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ServerId != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(ServerId);
      }
      if (ServerName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ServerName);
      }
      if (Id != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Name);
      }
      if (Level != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(Level);
      }
      if (IsCustomAvatar != false) {
        output.WriteRawTag(48);
        output.WriteBool(IsCustomAvatar);
      }
      if (HeadSystemAvatar != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(HeadBorder);
      }
      if (Power != 0UL) {
        output.WriteRawTag(80);
        output.WriteUInt64(Power);
      }
      if (LeagueName.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(LeagueName);
      }
      if (Lang != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(Lang);
      }
      if (Gender != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(Gender);
      }
      if (VipLevel != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(VipLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ServerId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerId);
      }
      if (ServerName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ServerName);
      }
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Level);
      }
      if (IsCustomAvatar != false) {
        size += 1 + 1;
      }
      if (HeadSystemAvatar != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(HeadSystemAvatar);
      }
      if (HeadCustomAvatar.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(HeadCustomAvatar);
      }
      if (HeadBorder != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(HeadBorder);
      }
      if (Power != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Power);
      }
      if (LeagueName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(LeagueName);
      }
      if (Lang != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Lang);
      }
      if (Gender != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Gender);
      }
      if (VipLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(VipLevel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamRole other) {
      if (other == null) {
        return;
      }
      if (other.ServerId != 0) {
        ServerId = other.ServerId;
      }
      if (other.ServerName.Length != 0) {
        ServerName = other.ServerName;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.IsCustomAvatar != false) {
        IsCustomAvatar = other.IsCustomAvatar;
      }
      if (other.HeadSystemAvatar != 0) {
        HeadSystemAvatar = other.HeadSystemAvatar;
      }
      if (other.HeadCustomAvatar.Length != 0) {
        HeadCustomAvatar = other.HeadCustomAvatar;
      }
      if (other.HeadBorder != 0) {
        HeadBorder = other.HeadBorder;
      }
      if (other.Power != 0UL) {
        Power = other.Power;
      }
      if (other.LeagueName.Length != 0) {
        LeagueName = other.LeagueName;
      }
      if (other.Lang != 0) {
        Lang = other.Lang;
      }
      if (other.Gender != 0) {
        Gender = other.Gender;
      }
      if (other.VipLevel != 0) {
        VipLevel = other.VipLevel;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 18: {
            ServerName = input.ReadString();
            break;
          }
          case 24: {
            Id = input.ReadUInt64();
            break;
          }
          case 34: {
            Name = input.ReadString();
            break;
          }
          case 40: {
            Level = input.ReadUInt32();
            break;
          }
          case 48: {
            IsCustomAvatar = input.ReadBool();
            break;
          }
          case 56: {
            HeadSystemAvatar = input.ReadUInt32();
            break;
          }
          case 66: {
            HeadCustomAvatar = input.ReadString();
            break;
          }
          case 72: {
            HeadBorder = input.ReadUInt32();
            break;
          }
          case 80: {
            Power = input.ReadUInt64();
            break;
          }
          case 90: {
            LeagueName = input.ReadString();
            break;
          }
          case 96: {
            Lang = input.ReadInt32();
            break;
          }
          case 104: {
            Gender = input.ReadInt32();
            break;
          }
          case 112: {
            VipLevel = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            ServerId = input.ReadUInt32();
            break;
          }
          case 18: {
            ServerName = input.ReadString();
            break;
          }
          case 24: {
            Id = input.ReadUInt64();
            break;
          }
          case 34: {
            Name = input.ReadString();
            break;
          }
          case 40: {
            Level = input.ReadUInt32();
            break;
          }
          case 48: {
            IsCustomAvatar = input.ReadBool();
            break;
          }
          case 56: {
            HeadSystemAvatar = input.ReadUInt32();
            break;
          }
          case 66: {
            HeadCustomAvatar = input.ReadString();
            break;
          }
          case 72: {
            HeadBorder = input.ReadUInt32();
            break;
          }
          case 80: {
            Power = input.ReadUInt64();
            break;
          }
          case 90: {
            LeagueName = input.ReadString();
            break;
          }
          case 96: {
            Lang = input.ReadInt32();
            break;
          }
          case 104: {
            Gender = input.ReadInt32();
            break;
          }
          case 112: {
            VipLevel = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 队伍中英雄信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamHero : pb::IMessage<TeamHero>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamHero> _parser = new pb::MessageParser<TeamHero>(() => new TeamHero());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamHero> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHero() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHero(TeamHero other) : this() {
      code_ = other.code_;
      pos_ = other.pos_;
      level_ = other.level_;
      starStage_ = other.starStage_;
      power_ = other.power_;
      skills_ = other.skills_.Clone();
      attrs_ = other.attrs_.Clone();
      soilder_ = other.soilder_ != null ? other.soilder_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHero Clone() {
      return new TeamHero(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int CodeFieldNumber = 1;
    private global::PbGameconfig.itemid code_ = global::PbGameconfig.itemid.Nil;
    /// <summary>
    /// 英雄 id, hero_config.xlsx - id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.itemid Code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "pos" field.</summary>
    public const int PosFieldNumber = 2;
    private int pos_;
    /// <summary>
    /// 位置, 1 ~ 5
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Pos {
      get { return pos_; }
      set {
        pos_ = value;
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 3;
    private uint level_;
    /// <summary>
    /// 等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "star_stage" field.</summary>
    public const int StarStageFieldNumber = 4;
    private uint starStage_;
    /// <summary>
    /// 星阶
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint StarStage {
      get { return starStage_; }
      set {
        starStage_ = value;
      }
    }

    /// <summary>Field number for the "power" field.</summary>
    public const int PowerFieldNumber = 5;
    private ulong power_;
    /// <summary>
    /// 战斗力
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Power {
      get { return power_; }
      set {
        power_ = value;
      }
    }

    /// <summary>Field number for the "skills" field.</summary>
    public const int SkillsFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Battle.TeamHeroSkill> _repeated_skills_codec
        = pb::FieldCodec.ForMessage(50, global::Battle.TeamHeroSkill.Parser);
    private readonly pbc::RepeatedField<global::Battle.TeamHeroSkill> skills_ = new pbc::RepeatedField<global::Battle.TeamHeroSkill>();
    /// <summary>
    /// 技能信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Battle.TeamHeroSkill> Skills {
      get { return skills_; }
    }

    /// <summary>Field number for the "attrs" field.</summary>
    public const int AttrsFieldNumber = 7;
    private static readonly pb::FieldCodec<global::Common.Attr> _repeated_attrs_codec
        = pb::FieldCodec.ForMessage(58, global::Common.Attr.Parser);
    private readonly pbc::RepeatedField<global::Common.Attr> attrs_ = new pbc::RepeatedField<global::Common.Attr>();
    /// <summary>
    /// 属性
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.Attr> Attrs {
      get { return attrs_; }
    }

    /// <summary>Field number for the "soilder" field.</summary>
    public const int SoilderFieldNumber = 8;
    private global::Battle.TeamHeroSoldier soilder_;
    /// <summary>
    /// 带兵量信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.TeamHeroSoldier Soilder {
      get { return soilder_; }
      set {
        soilder_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamHero);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamHero other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Code != other.Code) return false;
      if (Pos != other.Pos) return false;
      if (Level != other.Level) return false;
      if (StarStage != other.StarStage) return false;
      if (Power != other.Power) return false;
      if(!skills_.Equals(other.skills_)) return false;
      if(!attrs_.Equals(other.attrs_)) return false;
      if (!object.Equals(Soilder, other.Soilder)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Code != global::PbGameconfig.itemid.Nil) hash ^= Code.GetHashCode();
      if (Pos != 0) hash ^= Pos.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (StarStage != 0) hash ^= StarStage.GetHashCode();
      if (Power != 0UL) hash ^= Power.GetHashCode();
      hash ^= skills_.GetHashCode();
      hash ^= attrs_.GetHashCode();
      if (soilder_ != null) hash ^= Soilder.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Code != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Code);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Level);
      }
      if (StarStage != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(StarStage);
      }
      if (Power != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(Power);
      }
      skills_.WriteTo(output, _repeated_skills_codec);
      attrs_.WriteTo(output, _repeated_attrs_codec);
      if (soilder_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Soilder);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Code != global::PbGameconfig.itemid.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Code);
      }
      if (Pos != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Pos);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Level);
      }
      if (StarStage != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(StarStage);
      }
      if (Power != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(Power);
      }
      skills_.WriteTo(ref output, _repeated_skills_codec);
      attrs_.WriteTo(ref output, _repeated_attrs_codec);
      if (soilder_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Soilder);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Code != global::PbGameconfig.itemid.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Code);
      }
      if (Pos != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Pos);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Level);
      }
      if (StarStage != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(StarStage);
      }
      if (Power != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Power);
      }
      size += skills_.CalculateSize(_repeated_skills_codec);
      size += attrs_.CalculateSize(_repeated_attrs_codec);
      if (soilder_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Soilder);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamHero other) {
      if (other == null) {
        return;
      }
      if (other.Code != global::PbGameconfig.itemid.Nil) {
        Code = other.Code;
      }
      if (other.Pos != 0) {
        Pos = other.Pos;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.StarStage != 0) {
        StarStage = other.StarStage;
      }
      if (other.Power != 0UL) {
        Power = other.Power;
      }
      skills_.Add(other.skills_);
      attrs_.Add(other.attrs_);
      if (other.soilder_ != null) {
        if (soilder_ == null) {
          Soilder = new global::Battle.TeamHeroSoldier();
        }
        Soilder.MergeFrom(other.Soilder);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Code = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
          case 24: {
            Level = input.ReadUInt32();
            break;
          }
          case 32: {
            StarStage = input.ReadUInt32();
            break;
          }
          case 40: {
            Power = input.ReadUInt64();
            break;
          }
          case 50: {
            skills_.AddEntriesFrom(input, _repeated_skills_codec);
            break;
          }
          case 58: {
            attrs_.AddEntriesFrom(input, _repeated_attrs_codec);
            break;
          }
          case 66: {
            if (soilder_ == null) {
              Soilder = new global::Battle.TeamHeroSoldier();
            }
            input.ReadMessage(Soilder);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Code = (global::PbGameconfig.itemid) input.ReadEnum();
            break;
          }
          case 16: {
            Pos = input.ReadInt32();
            break;
          }
          case 24: {
            Level = input.ReadUInt32();
            break;
          }
          case 32: {
            StarStage = input.ReadUInt32();
            break;
          }
          case 40: {
            Power = input.ReadUInt64();
            break;
          }
          case 50: {
            skills_.AddEntriesFrom(ref input, _repeated_skills_codec);
            break;
          }
          case 58: {
            attrs_.AddEntriesFrom(ref input, _repeated_attrs_codec);
            break;
          }
          case 66: {
            if (soilder_ == null) {
              Soilder = new global::Battle.TeamHeroSoldier();
            }
            input.ReadMessage(Soilder);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 队伍中英雄技能
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamHeroSkill : pb::IMessage<TeamHeroSkill>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamHeroSkill> _parser = new pb::MessageParser<TeamHeroSkill>(() => new TeamHeroSkill());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamHeroSkill> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHeroSkill() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHeroSkill(TeamHeroSkill other) : this() {
      id_ = other.id_;
      groupId_ = other.groupId_;
      level_ = other.level_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHeroSkill Clone() {
      return new TeamHeroSkill(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    /// <summary>
    /// 技能 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "group_id" field.</summary>
    public const int GroupIdFieldNumber = 2;
    private int groupId_;
    /// <summary>
    /// 技能组 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int GroupId {
      get { return groupId_; }
      set {
        groupId_ = value;
      }
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 3;
    private int level_;
    /// <summary>
    /// 技能等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamHeroSkill);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamHeroSkill other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (GroupId != other.GroupId) return false;
      if (Level != other.Level) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (GroupId != 0) hash ^= GroupId.GetHashCode();
      if (Level != 0) hash ^= Level.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (GroupId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(GroupId);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (GroupId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(GroupId);
      }
      if (Level != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Level);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (GroupId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(GroupId);
      }
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Level);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamHeroSkill other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.GroupId != 0) {
        GroupId = other.GroupId;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            GroupId = input.ReadInt32();
            break;
          }
          case 24: {
            Level = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            GroupId = input.ReadInt32();
            break;
          }
          case 24: {
            Level = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 队伍中带兵量信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TeamHeroSoldier : pb::IMessage<TeamHeroSoldier>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TeamHeroSoldier> _parser = new pb::MessageParser<TeamHeroSoldier>(() => new TeamHeroSoldier());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TeamHeroSoldier> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHeroSoldier() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHeroSoldier(TeamHeroSoldier other) : this() {
      level_ = other.level_;
      num_ = other.num_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TeamHeroSoldier Clone() {
      return new TeamHeroSoldier(this);
    }

    /// <summary>Field number for the "level" field.</summary>
    public const int LevelFieldNumber = 1;
    private int level_;
    /// <summary>
    /// 等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Level {
      get { return level_; }
      set {
        level_ = value;
      }
    }

    /// <summary>Field number for the "num" field.</summary>
    public const int NumFieldNumber = 2;
    private int num_;
    /// <summary>
    /// 数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Num {
      get { return num_; }
      set {
        num_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TeamHeroSoldier);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TeamHeroSoldier other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Level != other.Level) return false;
      if (Num != other.Num) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Level != 0) hash ^= Level.GetHashCode();
      if (Num != 0) hash ^= Num.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Level != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Level);
      }
      if (Num != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Num);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Level != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Level);
      }
      if (Num != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Num);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Level);
      }
      if (Num != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Num);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TeamHeroSoldier other) {
      if (other == null) {
        return;
      }
      if (other.Level != 0) {
        Level = other.Level;
      }
      if (other.Num != 0) {
        Num = other.Num;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Level = input.ReadInt32();
            break;
          }
          case 16: {
            Num = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Level = input.ReadInt32();
            break;
          }
          case 16: {
            Num = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战斗统计信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleStats : pb::IMessage<BattleStats>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleStats> _parser = new pb::MessageParser<BattleStats>(() => new BattleStats());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleStats> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleStats() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleStats(BattleStats other) : this() {
      duration_ = other.duration_;
      attacker_ = other.attacker_.Clone();
      defender_ = other.defender_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleStats Clone() {
      return new BattleStats(this);
    }

    /// <summary>Field number for the "duration" field.</summary>
    public const int DurationFieldNumber = 1;
    private int duration_;
    /// <summary>
    /// 战斗时长
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Duration {
      get { return duration_; }
      set {
        duration_ = value;
      }
    }

    /// <summary>Field number for the "attacker" field.</summary>
    public const int AttackerFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Battle.BattleTeamStats> _repeated_attacker_codec
        = pb::FieldCodec.ForMessage(18, global::Battle.BattleTeamStats.Parser);
    private readonly pbc::RepeatedField<global::Battle.BattleTeamStats> attacker_ = new pbc::RepeatedField<global::Battle.BattleTeamStats>();
    /// <summary>
    /// 进攻方统计
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Battle.BattleTeamStats> Attacker {
      get { return attacker_; }
    }

    /// <summary>Field number for the "defender" field.</summary>
    public const int DefenderFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Battle.BattleTeamStats> _repeated_defender_codec
        = pb::FieldCodec.ForMessage(26, global::Battle.BattleTeamStats.Parser);
    private readonly pbc::RepeatedField<global::Battle.BattleTeamStats> defender_ = new pbc::RepeatedField<global::Battle.BattleTeamStats>();
    /// <summary>
    /// 防守方统计
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Battle.BattleTeamStats> Defender {
      get { return defender_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleStats);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleStats other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Duration != other.Duration) return false;
      if(!attacker_.Equals(other.attacker_)) return false;
      if(!defender_.Equals(other.defender_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Duration != 0) hash ^= Duration.GetHashCode();
      hash ^= attacker_.GetHashCode();
      hash ^= defender_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Duration != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Duration);
      }
      attacker_.WriteTo(output, _repeated_attacker_codec);
      defender_.WriteTo(output, _repeated_defender_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Duration != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Duration);
      }
      attacker_.WriteTo(ref output, _repeated_attacker_codec);
      defender_.WriteTo(ref output, _repeated_defender_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Duration != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Duration);
      }
      size += attacker_.CalculateSize(_repeated_attacker_codec);
      size += defender_.CalculateSize(_repeated_defender_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleStats other) {
      if (other == null) {
        return;
      }
      if (other.Duration != 0) {
        Duration = other.Duration;
      }
      attacker_.Add(other.attacker_);
      defender_.Add(other.defender_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Duration = input.ReadInt32();
            break;
          }
          case 18: {
            attacker_.AddEntriesFrom(input, _repeated_attacker_codec);
            break;
          }
          case 26: {
            defender_.AddEntriesFrom(input, _repeated_defender_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Duration = input.ReadInt32();
            break;
          }
          case 18: {
            attacker_.AddEntriesFrom(ref input, _repeated_attacker_codec);
            break;
          }
          case 26: {
            defender_.AddEntriesFrom(ref input, _repeated_defender_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleTeamStats : pb::IMessage<BattleTeamStats>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleTeamStats> _parser = new pb::MessageParser<BattleTeamStats>(() => new BattleTeamStats());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleTeamStats> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamStats() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamStats(BattleTeamStats other) : this() {
      pos_ = other.pos_;
      attack_ = other.attack_;
      defend_ = other.defend_;
      buff_ = other.buff_;
      debuff_ = other.debuff_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleTeamStats Clone() {
      return new BattleTeamStats(this);
    }

    /// <summary>Field number for the "pos" field.</summary>
    public const int PosFieldNumber = 1;
    private int pos_;
    /// <summary>
    /// 位置, 1-5
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Pos {
      get { return pos_; }
      set {
        pos_ = value;
      }
    }

    /// <summary>Field number for the "attack" field.</summary>
    public const int AttackFieldNumber = 2;
    private long attack_;
    /// <summary>
    /// 输出
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Attack {
      get { return attack_; }
      set {
        attack_ = value;
      }
    }

    /// <summary>Field number for the "defend" field.</summary>
    public const int DefendFieldNumber = 3;
    private long defend_;
    /// <summary>
    /// 承伤
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Defend {
      get { return defend_; }
      set {
        defend_ = value;
      }
    }

    /// <summary>Field number for the "buff" field.</summary>
    public const int BuffFieldNumber = 4;
    private long buff_;
    /// <summary>
    /// 增益
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Buff {
      get { return buff_; }
      set {
        buff_ = value;
      }
    }

    /// <summary>Field number for the "debuff" field.</summary>
    public const int DebuffFieldNumber = 5;
    private long debuff_;
    /// <summary>
    /// 削弱
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Debuff {
      get { return debuff_; }
      set {
        debuff_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleTeamStats);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleTeamStats other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Pos != other.Pos) return false;
      if (Attack != other.Attack) return false;
      if (Defend != other.Defend) return false;
      if (Buff != other.Buff) return false;
      if (Debuff != other.Debuff) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Pos != 0) hash ^= Pos.GetHashCode();
      if (Attack != 0L) hash ^= Attack.GetHashCode();
      if (Defend != 0L) hash ^= Defend.GetHashCode();
      if (Buff != 0L) hash ^= Buff.GetHashCode();
      if (Debuff != 0L) hash ^= Debuff.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Pos != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Pos);
      }
      if (Attack != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Attack);
      }
      if (Defend != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Defend);
      }
      if (Buff != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Buff);
      }
      if (Debuff != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Debuff);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Pos != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Pos);
      }
      if (Attack != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Attack);
      }
      if (Defend != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Defend);
      }
      if (Buff != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Buff);
      }
      if (Debuff != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Debuff);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Pos != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Pos);
      }
      if (Attack != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Attack);
      }
      if (Defend != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Defend);
      }
      if (Buff != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Buff);
      }
      if (Debuff != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Debuff);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleTeamStats other) {
      if (other == null) {
        return;
      }
      if (other.Pos != 0) {
        Pos = other.Pos;
      }
      if (other.Attack != 0L) {
        Attack = other.Attack;
      }
      if (other.Defend != 0L) {
        Defend = other.Defend;
      }
      if (other.Buff != 0L) {
        Buff = other.Buff;
      }
      if (other.Debuff != 0L) {
        Debuff = other.Debuff;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Pos = input.ReadInt32();
            break;
          }
          case 16: {
            Attack = input.ReadInt64();
            break;
          }
          case 24: {
            Defend = input.ReadInt64();
            break;
          }
          case 32: {
            Buff = input.ReadInt64();
            break;
          }
          case 40: {
            Debuff = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Pos = input.ReadInt32();
            break;
          }
          case 16: {
            Attack = input.ReadInt64();
            break;
          }
          case 24: {
            Defend = input.ReadInt64();
            break;
          }
          case 32: {
            Buff = input.ReadInt64();
            break;
          }
          case 40: {
            Debuff = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战报
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Report : pb::IMessage<Report>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Report> _parser = new pb::MessageParser<Report>(() => new Report());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Report> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Report() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Report(Report other) : this() {
      reportId_ = other.reportId_;
      attacker_ = other.attacker_ != null ? other.attacker_.Clone() : null;
      defender_ = other.defender_ != null ? other.defender_.Clone() : null;
      battleType_ = other.battleType_;
      result_ = other.result_;
      actions_ = other.actions_.Clone();
      stats_ = other.stats_ != null ? other.stats_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Report Clone() {
      return new Report(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "attacker" field.</summary>
    public const int AttackerFieldNumber = 2;
    private global::Battle.Team attacker_;
    /// <summary>
    /// 进攻方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.Team Attacker {
      get { return attacker_; }
      set {
        attacker_ = value;
      }
    }

    /// <summary>Field number for the "defender" field.</summary>
    public const int DefenderFieldNumber = 3;
    private global::Battle.Team defender_;
    /// <summary>
    /// 防御方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.Team Defender {
      get { return defender_; }
      set {
        defender_ = value;
      }
    }

    /// <summary>Field number for the "battle_type" field.</summary>
    public const int BattleTypeFieldNumber = 4;
    private global::PbGameconfig.battle_types battleType_ = global::PbGameconfig.battle_types._0;
    /// <summary>
    /// 战斗类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.battle_types BattleType {
      get { return battleType_; }
      set {
        battleType_ = value;
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 5;
    private global::Battle.BattleResult result_ = global::Battle.BattleResult.ResultNil;
    /// <summary>
    /// 战斗结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattleResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    /// <summary>Field number for the "actions" field.</summary>
    public const int ActionsFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Battle.Action> _repeated_actions_codec
        = pb::FieldCodec.ForMessage(50, global::Battle.Action.Parser);
    private readonly pbc::RepeatedField<global::Battle.Action> actions_ = new pbc::RepeatedField<global::Battle.Action>();
    /// <summary>
    /// 战斗过程
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Battle.Action> Actions {
      get { return actions_; }
    }

    /// <summary>Field number for the "stats" field.</summary>
    public const int StatsFieldNumber = 7;
    private global::Battle.BattleStats stats_;
    /// <summary>
    /// 战斗统计信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattleStats Stats {
      get { return stats_; }
      set {
        stats_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Report);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Report other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      if (!object.Equals(Attacker, other.Attacker)) return false;
      if (!object.Equals(Defender, other.Defender)) return false;
      if (BattleType != other.BattleType) return false;
      if (Result != other.Result) return false;
      if(!actions_.Equals(other.actions_)) return false;
      if (!object.Equals(Stats, other.Stats)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (attacker_ != null) hash ^= Attacker.GetHashCode();
      if (defender_ != null) hash ^= Defender.GetHashCode();
      if (BattleType != global::PbGameconfig.battle_types._0) hash ^= BattleType.GetHashCode();
      if (Result != global::Battle.BattleResult.ResultNil) hash ^= Result.GetHashCode();
      hash ^= actions_.GetHashCode();
      if (stats_ != null) hash ^= Stats.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (attacker_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Defender);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(32);
        output.WriteEnum((int) BattleType);
      }
      if (Result != global::Battle.BattleResult.ResultNil) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Result);
      }
      actions_.WriteTo(output, _repeated_actions_codec);
      if (stats_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Stats);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (attacker_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Attacker);
      }
      if (defender_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Defender);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(32);
        output.WriteEnum((int) BattleType);
      }
      if (Result != global::Battle.BattleResult.ResultNil) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Result);
      }
      actions_.WriteTo(ref output, _repeated_actions_codec);
      if (stats_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Stats);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (attacker_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Attacker);
      }
      if (defender_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Defender);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) BattleType);
      }
      if (Result != global::Battle.BattleResult.ResultNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Result);
      }
      size += actions_.CalculateSize(_repeated_actions_codec);
      if (stats_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Stats);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Report other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      if (other.attacker_ != null) {
        if (attacker_ == null) {
          Attacker = new global::Battle.Team();
        }
        Attacker.MergeFrom(other.Attacker);
      }
      if (other.defender_ != null) {
        if (defender_ == null) {
          Defender = new global::Battle.Team();
        }
        Defender.MergeFrom(other.Defender);
      }
      if (other.BattleType != global::PbGameconfig.battle_types._0) {
        BattleType = other.BattleType;
      }
      if (other.Result != global::Battle.BattleResult.ResultNil) {
        Result = other.Result;
      }
      actions_.Add(other.actions_);
      if (other.stats_ != null) {
        if (stats_ == null) {
          Stats = new global::Battle.BattleStats();
        }
        Stats.MergeFrom(other.Stats);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 18: {
            if (attacker_ == null) {
              Attacker = new global::Battle.Team();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 26: {
            if (defender_ == null) {
              Defender = new global::Battle.Team();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 32: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 40: {
            Result = (global::Battle.BattleResult) input.ReadEnum();
            break;
          }
          case 50: {
            actions_.AddEntriesFrom(input, _repeated_actions_codec);
            break;
          }
          case 58: {
            if (stats_ == null) {
              Stats = new global::Battle.BattleStats();
            }
            input.ReadMessage(Stats);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 18: {
            if (attacker_ == null) {
              Attacker = new global::Battle.Team();
            }
            input.ReadMessage(Attacker);
            break;
          }
          case 26: {
            if (defender_ == null) {
              Defender = new global::Battle.Team();
            }
            input.ReadMessage(Defender);
            break;
          }
          case 32: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 40: {
            Result = (global::Battle.BattleResult) input.ReadEnum();
            break;
          }
          case 50: {
            actions_.AddEntriesFrom(ref input, _repeated_actions_codec);
            break;
          }
          case 58: {
            if (stats_ == null) {
              Stats = new global::Battle.BattleStats();
            }
            input.ReadMessage(Stats);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战报存储
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ReportStorage : pb::IMessage<ReportStorage>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ReportStorage> _parser = new pb::MessageParser<ReportStorage>(() => new ReportStorage());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ReportStorage> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportStorage() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportStorage(ReportStorage other) : this() {
      reportId_ = other.reportId_;
      battleType_ = other.battleType_;
      battleResult_ = other.battleResult_;
      version_ = other.version_;
      random_ = other.random_.Clone();
      attacker_ = other.attacker_;
      defender_ = other.defender_;
      report_ = other.report_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportStorage Clone() {
      return new ReportStorage(this);
    }

    /// <summary>Field number for the "report_id" field.</summary>
    public const int ReportIdFieldNumber = 1;
    private string reportId_ = "";
    /// <summary>
    /// 战报 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ReportId {
      get { return reportId_; }
      set {
        reportId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "battle_type" field.</summary>
    public const int BattleTypeFieldNumber = 2;
    private global::PbGameconfig.battle_types battleType_ = global::PbGameconfig.battle_types._0;
    /// <summary>
    /// 战斗类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.battle_types BattleType {
      get { return battleType_; }
      set {
        battleType_ = value;
      }
    }

    /// <summary>Field number for the "battle_result" field.</summary>
    public const int BattleResultFieldNumber = 3;
    private global::Battle.BattleResult battleResult_ = global::Battle.BattleResult.ResultNil;
    /// <summary>
    /// 战斗结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.BattleResult BattleResult {
      get { return battleResult_; }
      set {
        battleResult_ = value;
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 4;
    private int version_;
    /// <summary>
    /// 战报版本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Version {
      get { return version_; }
      set {
        version_ = value;
      }
    }

    /// <summary>Field number for the "random" field.</summary>
    public const int RandomFieldNumber = 5;
    private static readonly pb::FieldCodec<int> _repeated_random_codec
        = pb::FieldCodec.ForInt32(42);
    private readonly pbc::RepeatedField<int> random_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// 随机数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> Random {
      get { return random_; }
    }

    /// <summary>Field number for the "attacker" field.</summary>
    public const int AttackerFieldNumber = 6;
    private ulong attacker_;
    /// <summary>
    /// 进攻方角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Attacker {
      get { return attacker_; }
      set {
        attacker_ = value;
      }
    }

    /// <summary>Field number for the "defender" field.</summary>
    public const int DefenderFieldNumber = 7;
    private ulong defender_;
    /// <summary>
    /// 防御方角色 id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Defender {
      get { return defender_; }
      set {
        defender_ = value;
      }
    }

    /// <summary>Field number for the "report" field.</summary>
    public const int ReportFieldNumber = 8;
    private pb::ByteString report_ = pb::ByteString.Empty;
    /// <summary>
    /// 战报, 见 Report 定义
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString Report {
      get { return report_; }
      set {
        report_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ReportStorage);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ReportStorage other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ReportId != other.ReportId) return false;
      if (BattleType != other.BattleType) return false;
      if (BattleResult != other.BattleResult) return false;
      if (Version != other.Version) return false;
      if(!random_.Equals(other.random_)) return false;
      if (Attacker != other.Attacker) return false;
      if (Defender != other.Defender) return false;
      if (Report != other.Report) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ReportId.Length != 0) hash ^= ReportId.GetHashCode();
      if (BattleType != global::PbGameconfig.battle_types._0) hash ^= BattleType.GetHashCode();
      if (BattleResult != global::Battle.BattleResult.ResultNil) hash ^= BattleResult.GetHashCode();
      if (Version != 0) hash ^= Version.GetHashCode();
      hash ^= random_.GetHashCode();
      if (Attacker != 0UL) hash ^= Attacker.GetHashCode();
      if (Defender != 0UL) hash ^= Defender.GetHashCode();
      if (Report.Length != 0) hash ^= Report.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) BattleType);
      }
      if (BattleResult != global::Battle.BattleResult.ResultNil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) BattleResult);
      }
      if (Version != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Version);
      }
      random_.WriteTo(output, _repeated_random_codec);
      if (Attacker != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(Attacker);
      }
      if (Defender != 0UL) {
        output.WriteRawTag(56);
        output.WriteUInt64(Defender);
      }
      if (Report.Length != 0) {
        output.WriteRawTag(66);
        output.WriteBytes(Report);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ReportId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ReportId);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) BattleType);
      }
      if (BattleResult != global::Battle.BattleResult.ResultNil) {
        output.WriteRawTag(24);
        output.WriteEnum((int) BattleResult);
      }
      if (Version != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Version);
      }
      random_.WriteTo(ref output, _repeated_random_codec);
      if (Attacker != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(Attacker);
      }
      if (Defender != 0UL) {
        output.WriteRawTag(56);
        output.WriteUInt64(Defender);
      }
      if (Report.Length != 0) {
        output.WriteRawTag(66);
        output.WriteBytes(Report);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ReportId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ReportId);
      }
      if (BattleType != global::PbGameconfig.battle_types._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) BattleType);
      }
      if (BattleResult != global::Battle.BattleResult.ResultNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) BattleResult);
      }
      if (Version != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Version);
      }
      size += random_.CalculateSize(_repeated_random_codec);
      if (Attacker != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Attacker);
      }
      if (Defender != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Defender);
      }
      if (Report.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(Report);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ReportStorage other) {
      if (other == null) {
        return;
      }
      if (other.ReportId.Length != 0) {
        ReportId = other.ReportId;
      }
      if (other.BattleType != global::PbGameconfig.battle_types._0) {
        BattleType = other.BattleType;
      }
      if (other.BattleResult != global::Battle.BattleResult.ResultNil) {
        BattleResult = other.BattleResult;
      }
      if (other.Version != 0) {
        Version = other.Version;
      }
      random_.Add(other.random_);
      if (other.Attacker != 0UL) {
        Attacker = other.Attacker;
      }
      if (other.Defender != 0UL) {
        Defender = other.Defender;
      }
      if (other.Report.Length != 0) {
        Report = other.Report;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 24: {
            BattleResult = (global::Battle.BattleResult) input.ReadEnum();
            break;
          }
          case 32: {
            Version = input.ReadInt32();
            break;
          }
          case 42:
          case 40: {
            random_.AddEntriesFrom(input, _repeated_random_codec);
            break;
          }
          case 48: {
            Attacker = input.ReadUInt64();
            break;
          }
          case 56: {
            Defender = input.ReadUInt64();
            break;
          }
          case 66: {
            Report = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ReportId = input.ReadString();
            break;
          }
          case 16: {
            BattleType = (global::PbGameconfig.battle_types) input.ReadEnum();
            break;
          }
          case 24: {
            BattleResult = (global::Battle.BattleResult) input.ReadEnum();
            break;
          }
          case 32: {
            Version = input.ReadInt32();
            break;
          }
          case 42:
          case 40: {
            random_.AddEntriesFrom(ref input, _repeated_random_codec);
            break;
          }
          case 48: {
            Attacker = input.ReadUInt64();
            break;
          }
          case 56: {
            Defender = input.ReadUInt64();
            break;
          }
          case 66: {
            Report = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战斗参数: 透传给其它业务模块
  /// 所有参数都是可选项
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattleLogicArgs : pb::IMessage<BattleLogicArgs>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattleLogicArgs> _parser = new pb::MessageParser<BattleLogicArgs>(() => new BattleLogicArgs());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattleLogicArgs> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleLogicArgs() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleLogicArgs(BattleLogicArgs other) : this() {
      isAutoFormation_ = other.isAutoFormation_;
      isAllowTeamEmpty_ = other.isAllowTeamEmpty_;
      attackerAddAttrs_ = other.attackerAddAttrs_.Clone();
      defenderAddAttrs_ = other.defenderAddAttrs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattleLogicArgs Clone() {
      return new BattleLogicArgs(this);
    }

    /// <summary>Field number for the "is_auto_formation" field.</summary>
    public const int IsAutoFormationFieldNumber = 1;
    private bool isAutoFormation_;
    /// <summary>
    /// 当队伍类型尚未布阵时，是否自动布阵
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsAutoFormation {
      get { return isAutoFormation_; }
      set {
        isAutoFormation_ = value;
      }
    }

    /// <summary>Field number for the "is_allow_team_empty" field.</summary>
    public const int IsAllowTeamEmptyFieldNumber = 2;
    private bool isAllowTeamEmpty_;
    /// <summary>
    /// 是否允许空队伍
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsAllowTeamEmpty {
      get { return isAllowTeamEmpty_; }
      set {
        isAllowTeamEmpty_ = value;
      }
    }

    /// <summary>Field number for the "attacker_add_attrs" field.</summary>
    public const int AttackerAddAttrsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Attr.ModuleAttrAdd> _repeated_attackerAddAttrs_codec
        = pb::FieldCodec.ForMessage(26, global::Attr.ModuleAttrAdd.Parser);
    private readonly pbc::RepeatedField<global::Attr.ModuleAttrAdd> attackerAddAttrs_ = new pbc::RepeatedField<global::Attr.ModuleAttrAdd>();
    /// <summary>
    /// 进攻方额外属性加成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Attr.ModuleAttrAdd> AttackerAddAttrs {
      get { return attackerAddAttrs_; }
    }

    /// <summary>Field number for the "defender_add_attrs" field.</summary>
    public const int DefenderAddAttrsFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Attr.ModuleAttrAdd> _repeated_defenderAddAttrs_codec
        = pb::FieldCodec.ForMessage(34, global::Attr.ModuleAttrAdd.Parser);
    private readonly pbc::RepeatedField<global::Attr.ModuleAttrAdd> defenderAddAttrs_ = new pbc::RepeatedField<global::Attr.ModuleAttrAdd>();
    /// <summary>
    /// 防守方额外属性加成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Attr.ModuleAttrAdd> DefenderAddAttrs {
      get { return defenderAddAttrs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattleLogicArgs);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattleLogicArgs other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (IsAutoFormation != other.IsAutoFormation) return false;
      if (IsAllowTeamEmpty != other.IsAllowTeamEmpty) return false;
      if(!attackerAddAttrs_.Equals(other.attackerAddAttrs_)) return false;
      if(!defenderAddAttrs_.Equals(other.defenderAddAttrs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (IsAutoFormation != false) hash ^= IsAutoFormation.GetHashCode();
      if (IsAllowTeamEmpty != false) hash ^= IsAllowTeamEmpty.GetHashCode();
      hash ^= attackerAddAttrs_.GetHashCode();
      hash ^= defenderAddAttrs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (IsAutoFormation != false) {
        output.WriteRawTag(8);
        output.WriteBool(IsAutoFormation);
      }
      if (IsAllowTeamEmpty != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsAllowTeamEmpty);
      }
      attackerAddAttrs_.WriteTo(output, _repeated_attackerAddAttrs_codec);
      defenderAddAttrs_.WriteTo(output, _repeated_defenderAddAttrs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (IsAutoFormation != false) {
        output.WriteRawTag(8);
        output.WriteBool(IsAutoFormation);
      }
      if (IsAllowTeamEmpty != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsAllowTeamEmpty);
      }
      attackerAddAttrs_.WriteTo(ref output, _repeated_attackerAddAttrs_codec);
      defenderAddAttrs_.WriteTo(ref output, _repeated_defenderAddAttrs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (IsAutoFormation != false) {
        size += 1 + 1;
      }
      if (IsAllowTeamEmpty != false) {
        size += 1 + 1;
      }
      size += attackerAddAttrs_.CalculateSize(_repeated_attackerAddAttrs_codec);
      size += defenderAddAttrs_.CalculateSize(_repeated_defenderAddAttrs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattleLogicArgs other) {
      if (other == null) {
        return;
      }
      if (other.IsAutoFormation != false) {
        IsAutoFormation = other.IsAutoFormation;
      }
      if (other.IsAllowTeamEmpty != false) {
        IsAllowTeamEmpty = other.IsAllowTeamEmpty;
      }
      attackerAddAttrs_.Add(other.attackerAddAttrs_);
      defenderAddAttrs_.Add(other.defenderAddAttrs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            IsAutoFormation = input.ReadBool();
            break;
          }
          case 16: {
            IsAllowTeamEmpty = input.ReadBool();
            break;
          }
          case 26: {
            attackerAddAttrs_.AddEntriesFrom(input, _repeated_attackerAddAttrs_codec);
            break;
          }
          case 34: {
            defenderAddAttrs_.AddEntriesFrom(input, _repeated_defenderAddAttrs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            IsAutoFormation = input.ReadBool();
            break;
          }
          case 16: {
            IsAllowTeamEmpty = input.ReadBool();
            break;
          }
          case 26: {
            attackerAddAttrs_.AddEntriesFrom(ref input, _repeated_attackerAddAttrs_codec);
            break;
          }
          case 34: {
            defenderAddAttrs_.AddEntriesFrom(ref input, _repeated_defenderAddAttrs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 战斗参数: 透传给战斗模块
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BattlePureArgs : pb::IMessage<BattlePureArgs>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BattlePureArgs> _parser = new pb::MessageParser<BattlePureArgs>(() => new BattlePureArgs());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BattlePureArgs> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattlePureArgs() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattlePureArgs(BattlePureArgs other) : this() {
      extra_ = other.extra_;
      isIgnoreDefender_ = other.isIgnoreDefender_;
      isIgnoreStoreReport_ = other.isIgnoreStoreReport_;
      random_ = other.random_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BattlePureArgs Clone() {
      return new BattlePureArgs(this);
    }

    /// <summary>Field number for the "extra" field.</summary>
    public const int ExtraFieldNumber = 1;
    private string extra_ = "";
    /// <summary>
    /// 战斗额外信息，原样透传给客户端，不需要时填空
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Extra {
      get { return extra_; }
      set {
        extra_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_ignore_defender" field.</summary>
    public const int IsIgnoreDefenderFieldNumber = 2;
    private bool isIgnoreDefender_;
    /// <summary>
    /// 忽略防御方战斗
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsIgnoreDefender {
      get { return isIgnoreDefender_; }
      set {
        isIgnoreDefender_ = value;
      }
    }

    /// <summary>Field number for the "is_ignore_store_report" field.</summary>
    public const int IsIgnoreStoreReportFieldNumber = 3;
    private bool isIgnoreStoreReport_;
    /// <summary>
    /// 忽略存储战报
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsIgnoreStoreReport {
      get { return isIgnoreStoreReport_; }
      set {
        isIgnoreStoreReport_ = value;
      }
    }

    /// <summary>Field number for the "random" field.</summary>
    public const int RandomFieldNumber = 4;
    private static readonly pb::FieldCodec<int> _repeated_random_codec
        = pb::FieldCodec.ForInt32(34);
    private readonly pbc::RepeatedField<int> random_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// 指定随机数，用于重演战斗
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> Random {
      get { return random_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BattlePureArgs);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BattlePureArgs other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Extra != other.Extra) return false;
      if (IsIgnoreDefender != other.IsIgnoreDefender) return false;
      if (IsIgnoreStoreReport != other.IsIgnoreStoreReport) return false;
      if(!random_.Equals(other.random_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Extra.Length != 0) hash ^= Extra.GetHashCode();
      if (IsIgnoreDefender != false) hash ^= IsIgnoreDefender.GetHashCode();
      if (IsIgnoreStoreReport != false) hash ^= IsIgnoreStoreReport.GetHashCode();
      hash ^= random_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Extra.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Extra);
      }
      if (IsIgnoreDefender != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsIgnoreDefender);
      }
      if (IsIgnoreStoreReport != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsIgnoreStoreReport);
      }
      random_.WriteTo(output, _repeated_random_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Extra.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Extra);
      }
      if (IsIgnoreDefender != false) {
        output.WriteRawTag(16);
        output.WriteBool(IsIgnoreDefender);
      }
      if (IsIgnoreStoreReport != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsIgnoreStoreReport);
      }
      random_.WriteTo(ref output, _repeated_random_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Extra.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Extra);
      }
      if (IsIgnoreDefender != false) {
        size += 1 + 1;
      }
      if (IsIgnoreStoreReport != false) {
        size += 1 + 1;
      }
      size += random_.CalculateSize(_repeated_random_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BattlePureArgs other) {
      if (other == null) {
        return;
      }
      if (other.Extra.Length != 0) {
        Extra = other.Extra;
      }
      if (other.IsIgnoreDefender != false) {
        IsIgnoreDefender = other.IsIgnoreDefender;
      }
      if (other.IsIgnoreStoreReport != false) {
        IsIgnoreStoreReport = other.IsIgnoreStoreReport;
      }
      random_.Add(other.random_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Extra = input.ReadString();
            break;
          }
          case 16: {
            IsIgnoreDefender = input.ReadBool();
            break;
          }
          case 24: {
            IsIgnoreStoreReport = input.ReadBool();
            break;
          }
          case 34:
          case 32: {
            random_.AddEntriesFrom(input, _repeated_random_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Extra = input.ReadString();
            break;
          }
          case 16: {
            IsIgnoreDefender = input.ReadBool();
            break;
          }
          case 24: {
            IsIgnoreStoreReport = input.ReadBool();
            break;
          }
          case 34:
          case 32: {
            random_.AddEntriesFrom(ref input, _repeated_random_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 一个动作，一场战斗由多个 Action 组成
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Action : pb::IMessage<Action>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Action> _parser = new pb::MessageParser<Action>(() => new Action());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Action> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Battle.BattleReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Action() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Action(Action other) : this() {
      frame_ = other.frame_;
      caster_ = other.caster_;
      target_ = other.target_.Clone();
      mask_ = other.mask_;
      action_ = other.action_;
      change_ = other.change_;
      value_ = other.value_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Action Clone() {
      return new Action(this);
    }

    /// <summary>Field number for the "frame" field.</summary>
    public const int FrameFieldNumber = 1;
    private int frame_;
    /// <summary>
    /// 时间帧，1秒 = 24 帧
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Frame {
      get { return frame_; }
      set {
        frame_ = value;
      }
    }

    /// <summary>Field number for the "caster" field.</summary>
    public const int CasterFieldNumber = 2;
    private int caster_;
    /// <summary>
    /// 动作发起者位置 1-5(进攻方), 11-15(防御方)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Caster {
      get { return caster_; }
      set {
        caster_ = value;
      }
    }

    /// <summary>Field number for the "target" field.</summary>
    public const int TargetFieldNumber = 3;
    private static readonly pb::FieldCodec<int> _repeated_target_codec
        = pb::FieldCodec.ForInt32(26);
    private readonly pbc::RepeatedField<int> target_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// 动作目标位置 1-5(进攻方), 11-15(防御方)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> Target {
      get { return target_; }
    }

    /// <summary>Field number for the "mask" field.</summary>
    public const int MaskFieldNumber = 4;
    private int mask_;
    /// <summary>
    /// 事件掩码，例如 0x0000 0001 目标闪避, 见服务端 define.go 事件掩码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Mask {
      get { return mask_; }
      set {
        mask_ = value;
      }
    }

    /// <summary>Field number for the "action" field.</summary>
    public const int Action_FieldNumber = 5;
    private global::Battle.ActionType action_ = global::Battle.ActionType.ActionNil;
    /// <summary>
    /// 动作类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.ActionType Action_ {
      get { return action_; }
      set {
        action_ = value;
      }
    }

    /// <summary>Field number for the "change" field.</summary>
    public const int ChangeFieldNumber = 6;
    private global::Battle.ChangeType change_ = global::Battle.ChangeType.ChangeNil;
    /// <summary>
    /// 变化类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Battle.ChangeType Change {
      get { return change_; }
      set {
        change_ = value;
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 7;
    private long value_;
    /// <summary>
    /// 变化值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Action);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Action other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Frame != other.Frame) return false;
      if (Caster != other.Caster) return false;
      if(!target_.Equals(other.target_)) return false;
      if (Mask != other.Mask) return false;
      if (Action_ != other.Action_) return false;
      if (Change != other.Change) return false;
      if (Value != other.Value) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Frame != 0) hash ^= Frame.GetHashCode();
      if (Caster != 0) hash ^= Caster.GetHashCode();
      hash ^= target_.GetHashCode();
      if (Mask != 0) hash ^= Mask.GetHashCode();
      if (Action_ != global::Battle.ActionType.ActionNil) hash ^= Action_.GetHashCode();
      if (Change != global::Battle.ChangeType.ChangeNil) hash ^= Change.GetHashCode();
      if (Value != 0L) hash ^= Value.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Frame != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Frame);
      }
      if (Caster != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Caster);
      }
      target_.WriteTo(output, _repeated_target_codec);
      if (Mask != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Mask);
      }
      if (Action_ != global::Battle.ActionType.ActionNil) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Action_);
      }
      if (Change != global::Battle.ChangeType.ChangeNil) {
        output.WriteRawTag(48);
        output.WriteEnum((int) Change);
      }
      if (Value != 0L) {
        output.WriteRawTag(56);
        output.WriteSInt64(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Frame != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Frame);
      }
      if (Caster != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Caster);
      }
      target_.WriteTo(ref output, _repeated_target_codec);
      if (Mask != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Mask);
      }
      if (Action_ != global::Battle.ActionType.ActionNil) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Action_);
      }
      if (Change != global::Battle.ChangeType.ChangeNil) {
        output.WriteRawTag(48);
        output.WriteEnum((int) Change);
      }
      if (Value != 0L) {
        output.WriteRawTag(56);
        output.WriteSInt64(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Frame != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Frame);
      }
      if (Caster != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Caster);
      }
      size += target_.CalculateSize(_repeated_target_codec);
      if (Mask != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Mask);
      }
      if (Action_ != global::Battle.ActionType.ActionNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Action_);
      }
      if (Change != global::Battle.ChangeType.ChangeNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Change);
      }
      if (Value != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeSInt64Size(Value);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Action other) {
      if (other == null) {
        return;
      }
      if (other.Frame != 0) {
        Frame = other.Frame;
      }
      if (other.Caster != 0) {
        Caster = other.Caster;
      }
      target_.Add(other.target_);
      if (other.Mask != 0) {
        Mask = other.Mask;
      }
      if (other.Action_ != global::Battle.ActionType.ActionNil) {
        Action_ = other.Action_;
      }
      if (other.Change != global::Battle.ChangeType.ChangeNil) {
        Change = other.Change;
      }
      if (other.Value != 0L) {
        Value = other.Value;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Frame = input.ReadInt32();
            break;
          }
          case 16: {
            Caster = input.ReadInt32();
            break;
          }
          case 26:
          case 24: {
            target_.AddEntriesFrom(input, _repeated_target_codec);
            break;
          }
          case 32: {
            Mask = input.ReadInt32();
            break;
          }
          case 40: {
            Action_ = (global::Battle.ActionType) input.ReadEnum();
            break;
          }
          case 48: {
            Change = (global::Battle.ChangeType) input.ReadEnum();
            break;
          }
          case 56: {
            Value = input.ReadSInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Frame = input.ReadInt32();
            break;
          }
          case 16: {
            Caster = input.ReadInt32();
            break;
          }
          case 26:
          case 24: {
            target_.AddEntriesFrom(ref input, _repeated_target_codec);
            break;
          }
          case 32: {
            Mask = input.ReadInt32();
            break;
          }
          case 40: {
            Action_ = (global::Battle.ActionType) input.ReadEnum();
            break;
          }
          case 48: {
            Change = (global::Battle.ChangeType) input.ReadEnum();
            break;
          }
          case 56: {
            Value = input.ReadSInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
