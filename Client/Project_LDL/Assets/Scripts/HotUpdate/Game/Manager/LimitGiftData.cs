using System;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Gift;
using UnityEngine;
using UnityEngine.Events;

//触发礼包、触发礼包
namespace Game.Hotfix
{
    public class LimitGiftData
    {
        public int DisplayId = 0;

        private Dictionary<int, string> iconPathDic;
        private List<GiftTrigger> giftTriggerList;
        
        public void Init()
        {
            giftTriggerList = new List<GiftTrigger>();
            iconPathDic = new Dictionary<int, string>();
            //触发礼包新增/修改推送
            RegisterProtoEvent(Protocol.MessageID.PushGiftTriggerChange, OnPushGiftTriggerChange);
            
            //触发礼包删除
            RegisterProtoEvent(Protocol.MessageID.PushGiftTriggerDel, OnPushGiftTriggerDel);
            
        }
        
        //注册协议推送逻辑
        private void RegisterProtoEvent(Protocol.MessageID id, UnityAction<object> callback = null)
        {
            var ProtoId = (int)id;
            NetEventDispatch.Instance.RegisterEvent(ProtoId, message =>
            {
                callback?.Invoke(message);
            });
        }
        
        //判断触发礼包功能入口解锁初始化逻辑
        public void CheckLimitGiftEntry(GameObject entryObj)
        {
            C2SGiftTriggerListReq((resp) =>
            {
                entryObj.gameObject.SetActive(HasLimitGift());
            });
        }

        //循环播放限时礼包icon
        public void DisplayGiftIcon()
        {
            
            
        }

        public void UpdateDisplayId()
        {
            
            
        }
        
        #region 红点逻辑
        
        
        
        
        #endregion
        
        #region 导表配置
        
        
        #endregion
        
        
        #region 协议
        
        //协议返回数据打印
        private void ProtoLog(bool isRequest,string protoName,object data)
        {
            bool isShow = true;
            if (isShow)
            {
                ColorLog.ProtoLog(isRequest,protoName,data);
            }
        }
        
        //触发礼包列表请求
        public void C2SGiftTriggerListReq(UnityAction<GiftTriggerListResp> callback = null)
        {
            var req = new GiftTriggerListReq();
            ProtoLog(true, "触发礼包列表", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.GiftTriggerList, req, (message) =>
            { 
                var resp = (GiftTriggerListResp)message; 
                ProtoLog(false, "触发礼包列表", resp);
               giftTriggerList.Clear();
               giftTriggerList.AddRange(resp.List);
               callback?.Invoke(resp);
               ToolScriptExtend.CheckAndRefreshForm<string>(EnumUIForm.UILimitGiftForm,"Refresh|0");
            });
        }
        
        //触发礼包新增/修改推送
        private void OnPushGiftTriggerChange(object message)
        {
            var resp = (PushGiftTriggerChange)message;
            ProtoLog(false, "触发礼包新增/修改推送", resp);
            if (giftTriggerList == null) return;
            foreach (var data in resp.List)
            {
                var index = giftTriggerList.FindIndex(x => x.Id == data.Id);
                if (index == -1)
                {
                    //礼包新增
                    giftTriggerList.Add(data);
                }
                else
                {
                    //礼包修改
                    giftTriggerList[index] = data;
                }
            }
            GameEntry.Event.Fire(LimitGiftChangeEventArgs.EventId, LimitGiftChangeEventArgs.Create());
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UILimitGiftForm);
            if (form == null)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UILimitGiftForm);
            }
            else
            {
                ToolScriptExtend.CheckAndRefreshForm<string>(EnumUIForm.UILimitGiftForm,"Refresh|0");
            }
        }
        
        //触发礼包删除推送
        private void OnPushGiftTriggerDel(object message)
        {
            var resp = (PushGiftTriggerDel)message;
            ProtoLog(false, "触发礼包删除推送", resp);
            if (resp.List.Count == 0) return;
            giftTriggerList.RemoveAll(x => resp.List.Contains(x.Id));
            ToolScriptExtend.CheckAndRefreshForm<string>(EnumUIForm.UILimitGiftForm,"Refresh|0");
            GameEntry.Event.Fire(LimitGiftChangeEventArgs.EventId, LimitGiftChangeEventArgs.Create());
        }
        
        #endregion

        public List<GiftTrigger> GetGiftList(bool needSort = true)
        {
            var list = new List<GiftTrigger>();
            foreach (var gift in giftTriggerList)
            {
                if (ToolScriptExtend.GetConfigById<gift_pack>(gift.Id, out var data))
                {
                    if (gift.BuyTimes < data.buy_limit_times)
                    {
                        if (gift.ExpiredTime == -1)
                        {
                            list.Add(gift);
                        }
                        else
                        {
                            if (TimeComponent.Now < gift.ExpiredTime)
                            {
                                list.Add(gift);
                            }
                        }
                    }
                }
            }

            if (needSort)
            {
                list.Sort((a, b) =>
                {
                    ToolScriptExtend.GetConfigById<gift_pack>(a.Id, out var dataA);
                    ToolScriptExtend.GetConfigById<gift_pack>(b.Id, out var dataB);
                    if (dataA.priority != dataB.priority) return dataA.priority.CompareTo(dataB.priority);
                    return a.Id.CompareTo(b.Id);
                }); 
            }
            return list;
        }

        //是否有触发礼包
        public bool HasLimitGift()
        {
            return giftTriggerList.Count > 0;
        }
        
    }
}
