using System.Collections.Generic;
using System.Linq;
using March;
using UnityEngine;

namespace Game.Hotfix
{
    public class MarchInfoWrapper
    {
        private MarchInfo m_MarchInfo;

        public MarchInfoWrapper(MarchInfo marchInfo = null)
        {
            m_MarchInfo = marchInfo;
        }
        
        /// <summary>
        /// 获得剩余士兵的百分比
        /// </summary>
        /// <returns></returns>
        public float GetSoldierPct()
        {
            return 0.5f;
        }

        /// <summary>
        /// 获取队伍剩余时间
        /// </summary>
        /// <returns></returns>
        public float? GetRemainTime()
        {
            return 5;
        }

        /// <summary>
        /// 获取行为标题
        /// </summary>
        /// <returns></returns>
        public string GetTitle()
        {
            return "采集中";
        }

        /// <summary>
        /// 获取行为描述
        /// </summary>
        /// <returns></returns>
        public string GetDes()
        {
            return "";
        }

        public bool IsEmpty()
        {
            return true;
        }
    }
    
    public class MarchData
    {
        private Dictionary<int,MarchInfoWrapper> m_MyMarchInfoDic;
        
        public void Init()
        {
            m_MyMarchInfoDic = new Dictionary<int, MarchInfoWrapper>();
        }


        public void AddMyMarchData(int teamId,MarchInfo info)
        {
            var wrapper = new MarchInfoWrapper(info);
            
            if (m_MyMarchInfoDic.ContainsKey(teamId))
            {
                m_MyMarchInfoDic.Remove(teamId);
            }
            m_MyMarchInfoDic
        }
        
        public List<MarchInfoWrapper> GetMyDataList()
        {
            return m_MyMarchInfoDic.Values.ToList();
        }

        public MarchInfoWrapper GetEmptyWrapper()
        {
            MarchInfoWrapper wrapper = new MarchInfoWrapper();
            return wrapper;
        }
        
    }
}