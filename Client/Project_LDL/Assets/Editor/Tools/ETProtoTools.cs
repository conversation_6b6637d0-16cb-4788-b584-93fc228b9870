using System;
using UnityEngine;
using UnityEditor;
using System.Diagnostics;
using System.IO;
using Debug = UnityEngine.Debug;

public class ETProtoTools : EditorWindow
{
    [MenuItem("Tools/生成C#协议")]
    public static void GenerateProto()
    {
        // 使用相对路径
        string protoDir = "../../Proto/proto"; // 相对于 Assets 目录的 Proto 文件目录
        string protoExePath = "../../Proto/protoc.exe"; // 相对于 Assets 目录的 protoc.exe 路径
        string outputDir = "Scripts/HotUpdate/Game/Proto"; // Assets 目录下的输出目录
        string protoFileName = "*.proto";

        // 获取绝对路径，相对于项目根目录
        string projectPath = Application.dataPath.Replace("/Assets", ""); // 获取项目根目录
        string absoluteProtoDir = Path.GetFullPath(Path.Combine(projectPath, protoDir));
        string absoluteProtoExePath = Path.GetFullPath(Path.Combine(projectPath, protoExePath));
        string absoluteOutputDir = Path.GetFullPath(Path.Combine(Application.dataPath, outputDir)); // Assets 目录下的绝对路径

        if (!Directory.Exists(absoluteOutputDir))
        {
            Directory.CreateDirectory(absoluteOutputDir);
        }

        string protoFiles = Path.Combine(absoluteProtoDir, protoFileName);

        string command = absoluteProtoExePath;
        string arguments = $" --csharp_out=\"{absoluteOutputDir}\" -I=\"{absoluteProtoDir}\" {protoFiles}";

        Debug.Log($"Running: {command} {arguments}");

        ProcessStartInfo psi = new ProcessStartInfo();
        psi.FileName = command;
        psi.Arguments = arguments;
        psi.UseShellExecute = false;
        psi.RedirectStandardError = true;
        psi.RedirectStandardOutput = true;
        psi.CreateNoWindow = true;

        Process process = new Process();
        process.StartInfo = psi;

        process.OutputDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data)) Debug.Log("Output: " + e.Data);
        };
        process.ErrorDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data)) Debug.LogError("Error: " + e.Data);
        };

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();
        process.WaitForExit();

        int exitCode = process.ExitCode;
        process.Close();

        if (exitCode == 0)
        {
            Debug.Log("Proto C# generation complete!");
            AssetDatabase.Refresh(); // 刷新Unity资源数据库
        }
        else
        {
            Debug.LogError("Proto C# generation failed. Exit code: " + exitCode);
        }
    }
    

    [MenuItem("Tools/生成C#协议配置")]
    public static void GenerateTabtoyConfig()
    {
        // 相对路径，相对于 Assets 目录
        string tabtoyExePath = "../../Proto/tabtoy.exe";
        string indexFile = "../../Proto/Index.csv";
        string csharpOutputDir = "Scripts/HotUpdate/GameFramework/LDLTable/configProto.cs";
        string binaryOutputDir = "ResPackage/DataTables";
        string customFileName = ".";
        
        // 获取绝对路径，相对于项目根目录
        string projectPath = Application.dataPath.Replace("/Assets", "");
        string absoluteTabtoyExePath = Path.GetFullPath(Path.Combine(projectPath, tabtoyExePath));
        string absoluteIndexFilePath = Path.GetFullPath(Path.Combine(projectPath, indexFile));
        string absoluteCsharpOutputDir = Path.GetFullPath(Path.Combine(Application.dataPath, csharpOutputDir));
        string absoluteBinaryOutputDir = Path.GetFullPath(Path.Combine(Application.dataPath, binaryOutputDir));

        if (!Directory.Exists(absoluteBinaryOutputDir))
        {
            Directory.CreateDirectory(absoluteBinaryOutputDir);
        }

        // 构建命令行参数
        string command = absoluteTabtoyExePath;
        string arguments = $" -mode=v3 -index=\"{absoluteIndexFilePath}\" -csharp_out=\"{absoluteCsharpOutputDir}\" -binary_dir=\"{absoluteBinaryOutputDir}\" -package=Game.Hotfix.Proto";

        Debug.Log($"Running: {command} {arguments}");

        ProcessStartInfo psi = new ProcessStartInfo();
        psi.FileName = command;
        psi.Arguments = arguments;
        psi.UseShellExecute = false;
        psi.RedirectStandardError = true;
        psi.RedirectStandardOutput = true;
        psi.CreateNoWindow = true;
        psi.WorkingDirectory = Path.GetDirectoryName(absoluteTabtoyExePath); // 非常重要: 设置工作目录

        Process process = new Process();
        process.StartInfo = psi;

        process.OutputDataReceived += (sender, e) => { if (!string.IsNullOrEmpty(e.Data)) Debug.Log("Output: " + e.Data); };
        process.ErrorDataReceived += (sender, e) => { if (!string.IsNullOrEmpty(e.Data)) Debug.LogError("Error: " + e.Data); };

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();
        process.WaitForExit();

        int exitCode = process.ExitCode;
        process.Close();

        try
        {
            string[] binFiles = Directory.GetFiles(absoluteBinaryOutputDir, "*.bin");
            foreach (string binFile in binFiles)
            {
                string bytesFile = Path.ChangeExtension(binFile, ".bytes");
                if (File.Exists(bytesFile))
                {
                    File.Delete(bytesFile);
                }
                File.Move(binFile, bytesFile);
                Debug.Log($"Renamed {binFile} to {bytesFile}");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to rename binary files: {e.Message}");
        }
        
        if (exitCode == 0)
        {
            Debug.Log("Tabtoy config generation complete!");
            AssetDatabase.Refresh();
        }
        else
        {
            Debug.LogError("Tabtoy config generation failed. Exit code: " + exitCode);
        }
    }

    [MenuItem("Tools/生成配置并移动")]
    public static void GenerateTabtoyTabConfig()
    {
        var excelPath = JumpToFolderMenuItems.GetTargetPath("ExcelFolder");
        if (excelPath == null) return;
        
        string tabtoyExePath = "exportC.bat";
        
        // 获取绝对路径，相对于项目根目录
        string projectPath = Application.dataPath.Replace("/Assets", "");
        string absoluteTabtoyExePath = Path.GetFullPath(Path.Combine(excelPath, tabtoyExePath));

        string csharpFileFrom = Path.GetFullPath(Path.Combine(excelPath, "out/client/config.cs"));
        string csharpFileTo = Path.GetFullPath(Path.Combine(Application.dataPath, "Scripts/HotUpdate/GameFramework/LDLTable/config.cs"));
        
        string BinaryDirFrom = Path.GetFullPath(Path.Combine(excelPath, "out/client"));
        string BinaryDirTo = Path.GetFullPath(Path.Combine(Application.dataPath, "ResPackage/DataTables"));
        
        // 构建命令行参数
        string command = absoluteTabtoyExePath;
        string arguments = "";

        Debug.Log($"Running: {command} {arguments}");

        ProcessStartInfo psi = new ProcessStartInfo();
        psi.FileName = command;
        psi.Arguments = arguments;
        psi.UseShellExecute = false;
        psi.RedirectStandardError = true;
        psi.RedirectStandardOutput = true;
        psi.CreateNoWindow = true;
        psi.WorkingDirectory = Path.GetDirectoryName(absoluteTabtoyExePath); // 非常重要: 设置工作目录

        Process process = new Process();
        process.StartInfo = psi;

        process.OutputDataReceived += (sender, e) => { if (!string.IsNullOrEmpty(e.Data)) Debug.Log("Output: " + e.Data); };
        process.ErrorDataReceived += (sender, e) => { if (!string.IsNullOrEmpty(e.Data)) Debug.LogError("Error: " + e.Data); };

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();
        process.WaitForExit();

        int exitCode = process.ExitCode;
        process.Close();

        try
        {
            //移动C#
            if (File.Exists(csharpFileTo))
            {
                File.Delete(csharpFileTo);
            }
            File.Move(csharpFileFrom, csharpFileTo);
            
            //移动bytes文件
            string[] binFiles = Directory.GetFiles(BinaryDirFrom, "*.bin");
            foreach (string binFile in binFiles)
            {
                string bytesFile = Path.ChangeExtension(binFile, ".bytes");

                var fileName = Path.GetFileName(bytesFile);
                var targetFile = Path.GetFullPath(Path.Combine(BinaryDirTo, fileName));
                
                if (File.Exists(targetFile))
                {
                    File.Delete(targetFile);
                }
                File.Copy(binFile, targetFile);
                // Debug.Log($"Renamed {binFile} to {targetFile}");
            }
            string resultString = string.Join(Environment.NewLine, binFiles);
            Debug.Log($"移动了 {binFiles.Length} \n {resultString}");
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to rename binary files: {e.Message}");
        }
        
        if (exitCode == 0)
        {
            Debug.Log("Tabtoy config generation complete!");
            AssetDatabase.Refresh();
        }
        else
        {
            Debug.LogError("Tabtoy config generation failed. Exit code: " + exitCode);
        }

    }
}